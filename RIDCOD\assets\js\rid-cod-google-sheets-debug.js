/**
 * RIDCOD Google Sheets Testing Functions
 * This file contains helper functions for testing Google Sheets data extraction
 */

// Function to test Google Sheets data extraction for a specific product/variation
function testRidCodGoogleSheetsData(productId, variationId = 0) {
    if (typeof rid_cod_params === 'undefined') {
        console.error('RIDCOD: rid_cod_params not found');
        return;
    }
    
    const data = {
        action: 'rid_cod_test_google_sheets_data',
        nonce: rid_cod_params.nonce,
        product_id: productId
    };
    
    if (variationId > 0) {
        data.variation_id = variationId;
    }
    
    jQuery.ajax({
        type: 'POST',
        url: rid_cod_params.ajax_url,
        data: data,
        success: function(response) {
            if (response.success) {
                console.log('✅ RIDCOD Google Sheets Test Results:', response.data);
                console.table({
                    'Product ID': response.data.product_id,
                    'Variation ID': response.data.variation_id,
                    'Product Type': response.data.product_type,
                    'Color': response.data.extracted_attributes.color,
                    'Size': response.data.extracted_attributes.size,
                    'Width (Other Attributes)': response.data.extracted_attributes.width
                });
            } else {
                console.error('❌ RIDCOD Google Sheets Test Failed:', response.data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ RIDCOD Google Sheets Test Error:', error);
        }
    });
}

// Function to test current form data
function testCurrentRidCodFormData() {
    const productId = jQuery('#rid-cod-form input[name="product_id"]').val();
    const variationId = jQuery('#rid-cod-form input[name="variation_id"]').val();
    
    if (!productId) {
        console.log('❌ RIDCOD: No product ID found in current form');
        return;
    }
    
    console.log('🧪 RIDCOD: Testing current form data...');
    testRidCodGoogleSheetsData(productId, variationId || 0);
}

// Function to test sending data to Google Sheets (Admin only)
function testRidCodGoogleSheetsSend(productId = 0, variationId = 0) {
    if (typeof rid_cod_params === 'undefined') {
        console.error('RIDCOD: rid_cod_params not found');
        return;
    }
    
    const data = {
        action: 'rid_cod_test_google_sheets_send',
        nonce: rid_cod_params.nonce
    };
    
    if (productId > 0) {
        data.product_id = productId;
    }
    
    if (variationId > 0) {
        data.variation_id = variationId;
    }
    
    console.log('📤 RIDCOD: Sending test data to Google Sheets...');
    
    jQuery.ajax({
        type: 'POST',
        url: rid_cod_params.ajax_url,
        data: data,
        success: function(response) {
            if (response.success) {
                console.log('✅ RIDCOD Google Sheets Send Test Success:', response.data);
                console.table({
                    'Status': 'نجح الإرسال',
                    'Response Code': response.data.response_code,
                    'Color Sent': response.data.test_data.color,
                    'Size Sent': response.data.test_data.size,
                    'Width Sent': response.data.test_data.width
                });
                alert('✅ نجح إرسال البيانات التجريبية إلى Google Sheets!\n\nتحقق من الشيت والـ Console للتفاصيل.');
            } else {
                console.error('❌ RIDCOD Google Sheets Send Test Failed:', response.data);
                alert('❌ فشل إرسال البيانات: ' + response.data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ RIDCOD Google Sheets Send Test Error:', error);
            alert('❌ خطأ في الشبكة: ' + error);
        }
    });
}

// Auto-run test if in development mode or if explicitly requested
if (typeof window !== 'undefined' && window.location.search.includes('rid_cod_debug=1')) {
    jQuery(document).ready(function($) {
        console.log('🔍 RIDCOD Google Sheets Debug Mode Enabled');
        console.log('💡 Use testCurrentRidCodFormData() to test current form data');
        console.log('💡 Use testRidCodGoogleSheetsData(productId, variationId) to test specific products');
        
        // Auto-test current form after 2 seconds if available
        setTimeout(function() {
            if ($('#rid-cod-form').length > 0) {
                console.log('🚀 Auto-testing current form data...');
                testCurrentRidCodFormData();
            }
        }, 2000);
    });
}
