<?php
/**
 * Various wrapping functions for easier custom fields creation.
 */

add_action( 'after_setup_theme', 'napoleon_setup_helpers_post_meta' );
function napoleon_setup_helpers_post_meta() {
	add_image_size( 'napoleon_featgal_small_thumb', 100, 100, true );

	add_action( 'wp_ajax_napoleon_featgal_AJAXPreview', 'napoleon_featgal_AJAXPreview' );
}

function napoleon_prepare_metabox( $post_type ) {
	wp_nonce_field( basename( __FILE__ ), $post_type . '_nonce' );
}

function napoleon_can_save_meta( $post_type ) {
	global $post;

	if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
		return false;
	}

	if ( ! isset( $_POST[ $post_type . '_nonce' ] ) || ! wp_verify_nonce( sanitize_key( wp_unslash( $_POST[ $post_type . '_nonce' ] ) ), basename( __FILE__ ) ) ) {
		return false;
	}

	if ( isset( $_POST['post_view'] ) && 'list' === $_POST['post_view'] ) {
		return false;
	}

	if ( ! isset( $_POST['post_type'] ) || $post_type !== $_POST['post_type'] ) {
		return false;
	}

	$post_type_obj = get_post_type_object( $post->post_type );
	if ( ! current_user_can( $post_type_obj->cap->edit_post, $post->ID ) ) {
		return false;
	}

	return true;
}

function napoleon_metabox_gallery( $gid = 1 ) {
	global $post;
	$post_id = $post->ID;

	napoleon_featgal_print_meta_html( $post_id, $gid );
}

function napoleon_metabox_gallery_save( $POST, $gid = 1 ) {
	global $post;
	$post_id = $post->ID;

	napoleon_featgal_update_meta( $post_id, $POST, $gid );
}

function napoleon_metabox_input( $fieldname, $label, $params = array() ) {
	global $post;

	$defaults = array(
		'label_class' => '',
		'input_class' => 'widefat',
		'input_type'  => 'text',
		'esc_func'    => 'esc_attr',
		'before'      => '<p class="at-field-group at-field-input">',
		'after'       => '</p>',
		'default'     => '',
	);
	$params = wp_parse_args( $params, $defaults );

	$custom_keys = get_post_custom_keys( $post->ID );

	if ( is_array( $custom_keys ) && in_array( $fieldname, $custom_keys, true ) && is_callable( $params['esc_func'] ) ) {
		$value = get_post_meta( $post->ID, $fieldname, true );
		$value = call_user_func( $params['esc_func'], $value );
	} else {
		$value = $params['default'];
	}

	echo $params['before'];

	if ( ! empty( $label ) ) {
		?><label for="<?php echo esc_attr( $fieldname ); ?>" class="<?php echo esc_attr( $params['label_class'] ); ?>"><?php echo wp_kses( $label, napoleon_get_allowed_tags() ); ?></label><?php
	}

	?><input id="<?php echo esc_attr( $fieldname ); ?>" type="<?php echo esc_attr( $params['input_type'] ); ?>" name="<?php echo esc_attr( $fieldname ); ?>" value="<?php echo esc_attr( $value ); ?>" class="<?php echo esc_attr( $params['input_class'] ); ?>" /><?php

	echo $params['after'];

}


function napoleon_metabox_textarea( $fieldname, $label, $params = array() ) {
	global $post;

	$defaults = array(
		'label_class' => '',
		'input_class' => 'widefat',
		'esc_func'    => 'esc_textarea',
		'before'      => '<p class="at-field-group at-field-textarea">',
		'after'       => '</p>',
		'default'     => '',
	);
	$params = wp_parse_args( $params, $defaults );

	$custom_keys = get_post_custom_keys( $post->ID );

	if ( is_array( $custom_keys ) && in_array( $fieldname, $custom_keys, true ) && is_callable( $params['esc_func'] ) ) {
		$value = get_post_meta( $post->ID, $fieldname, true );
		$value = call_user_func( $params['esc_func'], $value );
	} else {
		$value = $params['default'];
	}

	echo $params['before'];

	if ( ! empty( $label ) ) {
		?><label for="<?php echo esc_attr( $fieldname ); ?>" class="<?php echo esc_attr( $params['label_class'] ); ?>"><?php echo wp_kses( $label, napoleon_get_allowed_tags() ); ?></label><?php
	}

	?><textarea id="<?php echo esc_attr( $fieldname ); ?>" name="<?php echo esc_attr( $fieldname ); ?>" class="<?php echo esc_attr( $params['input_class'] ); ?>"><?php echo esc_textarea( $value ); ?></textarea><?php

	echo $params['after'];

}

function napoleon_metabox_dropdown( $fieldname, $options, $label, $params = array() ) {
	global $post;
	$options = (array) $options;

	$defaults = array(
		'before'  => '<p class="at-field-group at-field-dropdown">',
		'after'   => '</p>',
		'default' => '',
	);
	$params = wp_parse_args( $params, $defaults );

	$custom_keys = get_post_custom_keys( $post->ID );

	if ( is_array( $custom_keys ) && in_array( $fieldname, $custom_keys, true ) ) {
		$value = get_post_meta( $post->ID, $fieldname, true );
	} else {
		$value = $params['default'];
	}

	echo $params['before'];

	if ( ! empty( $label ) ) {
		?><label for="<?php echo esc_attr( $fieldname ); ?>"><?php echo wp_kses( $label, napoleon_get_allowed_tags() ); ?></label><?php
	}

	?>
		<select id="<?php echo esc_attr( $fieldname ); ?>" name="<?php echo esc_attr( $fieldname ); ?>">
			<?php foreach ( $options as $opt_val => $opt_label ) : ?>
				<option value="<?php echo esc_attr( $opt_val ); ?>" <?php selected( $value, $opt_val ); ?>><?php echo esc_html( $opt_label ); ?></option>
			<?php endforeach; ?>
		</select>
	<?php

	echo $params['after'];
}

// $fieldname is the actual name="" attribute common to all radios in the group.
// $optionname is the id of the radio, so that the label can be associated with it.
function napoleon_metabox_radio( $fieldname, $optionname, $optionval, $label, $params = array() ) {
	global $post;

	$defaults = array(
		'before'  => '<p class="at-field-group at-field-radio">',
		'after'   => '</p>',
		'default' => '',
	);
	$params = wp_parse_args( $params, $defaults );

	$custom_keys = get_post_custom_keys( $post->ID );

	if ( is_array( $custom_keys ) && in_array( $fieldname, $custom_keys, true ) ) {
		$value = get_post_meta( $post->ID, $fieldname, true );
	} else {
		$value = $params['default'];
	}

	echo $params['before'];
	?>
		<input type="radio" class="radio" id="<?php echo esc_attr( $optionname ); ?>" name="<?php echo esc_attr( $fieldname ); ?>" value="<?php echo esc_attr( $optionval ); ?>" <?php checked( $value, $optionval ); ?> />
		<label for="<?php echo esc_attr( $optionname ); ?>" class="radio"><?php echo wp_kses( $label, napoleon_get_allowed_tags() ); ?></label>
	<?php
	echo $params['after'];
}

function napoleon_metabox_checkbox( $fieldname, $value, $label, $params = array() ) {
	global $post;

	$defaults = array(
		'before'  => '<p class="at-field-group at-field-checkbox">',
		'after'   => '</p>',
		'default' => '',
	);
	$params = wp_parse_args( $params, $defaults );

	$custom_keys = get_post_custom_keys( $post->ID );

	if ( is_array( $custom_keys ) && in_array( $fieldname, $custom_keys, true ) ) {
		$checked = get_post_meta( $post->ID, $fieldname, true );
	} else {
		$checked = $params['default'];
	}

	echo $params['before'];
	?>
		<input type="checkbox" id="<?php echo esc_attr( $fieldname ); ?>" class="check" name="<?php echo esc_attr( $fieldname ); ?>" value="<?php echo esc_attr( $value ); ?>" <?php checked( $checked, $value ); ?> />
		<label for="<?php echo esc_attr( $fieldname ); ?>"><?php echo wp_kses( $label, napoleon_get_allowed_tags() ); ?></label>
	<?php
	echo $params['after'];
}

function napoleon_metabox_open_tab( $title ) {
	?>
	<div class="at-cf-section">
		<?php if ( ! empty( $title ) ) : ?>
			<h3 class="at-cf-title"><?php echo esc_html( $title ); ?></h3>
		<?php endif; ?>
		<div class="at-cf-inside">
	<?php
}

function napoleon_metabox_close_tab() {
	?>
		</div>
	</div>
	<?php
}

function napoleon_metabox_open_collapsible( $title ) {
	?>
	<div class="postbox" style="margin-top:20px">
		<div class="handlediv" title="<?php esc_attr_e( 'Click to toggle', 'napoleon' ); ?>"><br></div>
		<h3 class="hndle"><?php echo esc_html( $title ); ?></h3>
		<div class="inside">
	<?php
}

function napoleon_metabox_close_collapsible() {
	?>
		</div>
	</div>
	<?php
}

function napoleon_metabox_guide( $strings, $params = array() ) {
	$defaults = array(
		'type'        => 'auto', // auto, p, ol, ul
		'before'      => '',
		'before_each' => '',
		'after'       => '',
		'after_each'  => '',
	);
	$params = wp_parse_args( $params, $defaults );

	if ( empty( $strings ) ) {
		return;
	}

	if ( 'auto' === $params['type'] ) {
		if ( is_array( $strings ) && count( $strings ) > 1 ) {
			$params['type'] = 'ol';
		} else {
			$params['type'] = 'p';
		}
	}

	if ( is_string( $strings ) ) {
		$strings = array( $strings );
	}

	if ( 'p' === $params['type'] ) {
		$params['before_each'] = '<p class="at-cf-guide">';
		$params['after_each']  = '</p>';
	} elseif ( 'ol' === $params['type'] ) {
		$params['before']      = '<ol class="at-cf-guide">';
		$params['before_each'] = '<li>';
		$params['after']       = '</ol>';
		$params['after_each']  = '</li>';
	} elseif ( 'ul' === $params['type'] ) {
		$params['before']      = '<ul class="at-cf-guide">';
		$params['before_each'] = '<li>';
		$params['after']       = '</ul>';
		$params['after_each']  = '</li>';
	}

	echo $params['before'];
	foreach ( $strings as $string ) {
		echo $params['before_each'] . $string . $params['after_each'];
	}
	echo $params['after'];
}

function napoleon_bind_metabox_to_page_template( $metabox_id, $template_file, $js_var ) {
	if ( is_string( $template_file ) && ( '' === $template_file || 'default' === $template_file ) ) {
		$template_file = array( '', 'default' );
	} elseif ( is_array( $template_file ) && ( in_array( '', $template_file, true ) || in_array( 'default', $template_file, true ) ) ) {
		$template_file = array_unique( array_merge( $template_file, array( '', 'default' ) ) );
	}

	if ( is_array( $template_file ) ) {
		$template_file = implode( "', '", $template_file );
	}

	$css = sprintf( '<style type="text/css">%s { display: none; }</style>', '#' . $metabox_id );

	$js = <<<ENDJS
    (function($) {
		$('head').append('{$css}');

	    $(window).load( function() {
			var template_box = $( '#page_template, select.components-select-control__input' );

			var {$js_var} = $( '#{$metabox_id}' );
			if ( template_box.length > 0 ) {
				var {$js_var}_template = [ '{$template_file}' ];
		
				if ( $.inArray( template_box.val(), {$js_var}_template ) > -1 ) {
					{$js_var}.show();
				}
		
				template_box.change( function() {
					if ( $.inArray( template_box.val(), {$js_var}_template ) > -1 ) {
						{$js_var}.show();
						if ( typeof google === 'object' && typeof google.maps === 'object' ) {
							if ( {$js_var}.find( '.gllpLatlonPicker' ).length > 0 ) {
								google.maps.event.trigger( window, 'resize', {} );
							}
						}
					} else {
						{$js_var}.hide();
					}
				} );
			} else {
				{$js_var}.hide();
			}
	    } );
    })(jQuery);
ENDJS;

	wp_add_inline_script( 'napoleon-post-meta', $js );

	echo <<<HTML
        <script type="text/javascript">
        jQuery(document).ready( function($) {

            /**
             * Adjust visibility of the meta box at startup
            */
            if($('#page_template').val() == 'page-portfolio.php') {
                // show the meta box
                $('#postimagediv').show();
            } else {
                // hide your meta box
                $('#postimagediv').hide();
            }

             /**
             * Live adjustment of the meta box visibility
            */
            $('#page_template').live('change', function(){
                    if($(this).val() == 'page-portfolio.php') {
                    // show the meta box
                    $('#postimagediv').show();
                } else {
                    // hide your meta box
                    $('#postimagediv').hide();
                }

                // Debug only
                if (typeof console == "object") 
                    console.log ('live change value = ' + $(this).val());
            });                 
        });    
        </script>
HTML;

}

function napoleon_bind_metabox_to_post_format( $metabox_id, $post_format, $js_var ) {
	if ( is_array( $post_format ) ) {
		$post_format = implode( "', '", $post_format );
	}

	$css = sprintf( '<style type="text/css">%s { display: none; }</style>', '#' . $metabox_id );

	$js = <<<ENDJS
	(function($) {

		$('head').append('{$css}');

		$(window).load( function() {
			var formats_box = $('input[type=radio][name=post_format], .editor-post-format select');
			if ( formats_box.length > 0 ) {
				var {$js_var} = $('#{$metabox_id}');
				var {$js_var}_format = ['{$post_format}'];
		
				if ( $('body').hasClass('block-editor-page') ) {
					var post_format_selected = $( '.editor-post-format select' ).find(':selected').val();
				} else {
					var post_format_selected = $('#post-formats-select input.post-format:checked').val();
				}
			
				if( $.inArray( post_format_selected, {$js_var}_format ) > -1 ) {
					{$js_var}.show();
				}
		
				formats_box.change( function() {
					if( $.inArray( $(this).val(), {$js_var}_format ) > -1 ) {
						{$js_var}.show();
					} else {
						{$js_var}.hide();
					}
				} );
			}
		});
	})(jQuery);
ENDJS;

	wp_add_inline_script( 'napoleon-post-meta', $js );
}


/**
 * Creates the necessary gallery HTML code for use in metaboxes.
 *
 * @param int|bool $post_id The post ID where the gallery's default values should be loaded from. If empty, the global $post object's ID is used.
 * @param int $gid The gallery ID (instance). Only needed when a post has more than one galleries. Defaults to 1.
 * @return void
 */
function napoleon_featgal_print_meta_html( $post_id = false, $gid = 1 ) {
	if ( false === (bool) $post_id ) {
		global $post;
		$post_id = $post->ID;
	}

	$gid = absint( $gid );
	if ( $gid < 1 ) {
		$gid = 1;
	}

	$ids  = get_post_meta( $post_id, 'at_featured_gallery_' . $gid, true );
	$rand = get_post_meta( $post_id, 'at_featured_gallery_rand_' . $gid, true );

	$custom_keys = get_post_custom_keys( $post_id );

	?>
	<div class="at-media-manager-gallery">
		<input type="button" class="at-upload-to-gallery button" value="<?php esc_attr_e( 'Add Images', 'napoleon' ); ?>"/>
		<input type="hidden" class="at-upload-to-gallery-ids" name="at_featured_gallery_<?php echo esc_attr( $gid ); ?>" value="<?php echo esc_attr( $ids ); ?>"/>
		<p><label class="at-upload-to-gallery-random"><input type="checkbox" name="at_featured_gallery_rand_<?php echo esc_attr( $gid ); ?>" value="rand" <?php checked( $rand, 'rand' ); ?> /> <?php esc_html_e( 'Randomize order', 'napoleon' ); ?></label></p>
		<div class="at-upload-to-gallery-preview group">
			<?php
				$images = napoleon_featgal_get_images( $ids );
				if ( false !== $images && is_array( $images ) ) {
					foreach ( $images as $image ) {
						?>
						<div class="thumb">
							<img src="<?php echo esc_url( $image['url'] ); ?>" data-img-id="<?php echo esc_attr( $image['id'] ); ?>">
							<a href="#" class="close media-modal-icon" title="<?php esc_attr_e( 'Remove from gallery', 'napoleon' ); ?>"></a>
						</div>
						<?php
					}
				}
			?>
			<p class="at-upload-to-gallery-preview-text"><?php esc_html_e( 'Your gallery images will appear here', 'napoleon' ); ?></p>
		</div>
	</div>
	<?php
}

/**
 * Looks for gallery custom fields in an array, sanitizes and stores them in post meta.
 * Uses substr() so return values are the same.
 *
 * @param int $post_id The post ID where the gallery's custom fields should be stored.
 * @param array $POST An array that contains gallery custom field values. Usually $_POST should be passed.
 * @param int $gid The gallery ID (instance). Only needed when a post has more than one galleries. Defaults to 1.
 * @return bool True on success, boolean false on invalid parameters.
 */
function napoleon_featgal_update_meta( $post_id, $POST, $gid = 1 ) {
	if ( absint( $post_id ) < 1 ) {
		return false;
	}

	if ( ! is_array( $POST ) ) {
		return false;
	}

	$gid = absint( $gid );
	if ( $gid < 1 ) {
		$gid = 1;
	}

	$f_ids  = 'at_featured_gallery_' . $gid;
	$f_rand = 'at_featured_gallery_rand_' . $gid;

	$ids         = array();
	$ids_string  = '';
	$rand_string = '';
	if ( ! empty( $POST[ $f_ids ] ) ) {
		$ids = explode( ',', $POST[ $f_ids ] );
		$ids = array_filter( $ids );

		if ( count( $ids ) > 0 ) {
			$ids        = array_map( 'intval', $ids );
			$ids        = array_map( 'abs', $ids );
			$ids_string = implode( ',', $ids );
		}
	}

	if ( ! empty( $POST[ $f_rand ] ) && 'rand' === $POST[ $f_rand ] ) {
		$rand_string = 'rand';
	}

	update_post_meta( $post_id, $f_ids, $ids_string );
	update_post_meta( $post_id, $f_rand, $rand_string );

	return true;
}

function napoleon_featgal_get_ids( $post_id = false, $gid = 1 ) {
	if ( false === (bool) $post_id ) {
		global $post;
		$post_id = $post->ID;
	} else {
		$post_id = absint( $post_id );
	}

	$gid = absint( $gid );
	if ( $gid < 1 ) {
		$gid = 1;
	}

	$ids  = get_post_meta( $post_id, 'at_featured_gallery_' . $gid, true );
	$rand = get_post_meta( $post_id, 'at_featured_gallery_rand_' . $gid, true );

	$ids = explode( ',', $ids );
	$ids = array_filter( $ids );

	if ( 'rand' === $rand ) {
		shuffle( $ids );
	}

	return $ids;
}

function napoleon_featgal_get_attachments( $post_id = false, $gid = 1, $extra_args = array() ) {
	if ( false === (bool) $post_id ) {
		global $post;
		$post_id = $post->ID;
	} else {
		$post_id = absint( $post_id );
	}

	$gid = absint( $gid );
	if ( $gid < 1 ) {
		$gid = 1;
	}

	$ids  = get_post_meta( $post_id, 'at_featured_gallery_' . $gid, true );
	$rand = get_post_meta( $post_id, 'at_featured_gallery_rand_' . $gid, true );

	$ids = explode( ',', $ids );
	$ids = array_filter( $ids );

	$args = array(
		'post_type'        => 'attachment',
		'post_mime_type'   => 'image',
		'post_status'      => 'any',
		'posts_per_page'   => - 1,
		'suppress_filters' => true,
	);

	$custom_keys = get_post_custom_keys( $post_id );
	if ( is_null( $custom_keys ) ) {
		$custom_keys = array();
	}

	if ( ! in_array( 'at_featured_gallery_' . $gid, $custom_keys, true ) ) {
		$args['post_parent'] = $post_id;
		$args['order']       = 'ASC';
		$args['orderby']     = 'menu_order';
	} elseif ( count( $ids ) > 0 ) {
		$args['post__in'] = $ids;
		$args['orderby']  = 'post__in';

		if ( 'rand' === $rand ) {
			$args['orderby'] = 'rand';
		}
	} else {
		// Make sure we return an empty result set.
		$args['post__in'] = array( -1 );
	}

	if ( is_array( $extra_args ) && count( $extra_args ) > 0 ) {
		$args = array_merge( $args, $extra_args );
	}

	return new WP_Query( $args );
}

/**
 * Reads $_POST["ids"] for a comma separated list of image attachment IDs, prints a JSON array of image URLs and exits.
 * Hooked to wp_ajax_napoleon_featgal_AJAXPreview for AJAX updating of the galleries' previews.
 */
function napoleon_featgal_AJAXPreview() {
	$ids  = isset( $_POST['ids'] ) ? sanitize_text_field( $_POST['ids'] ) : ''; // Input var okay.
	$urls = napoleon_featgal_get_images( $ids );
	if ( false === $urls ) {
		echo 'FAIL';
		if ( defined( 'DOING_AJAX' ) && DOING_AJAX ) {
			wp_die();
		} else {
			die;
		}
	} else {
		wp_send_json( $urls );
	}
}

/**
 * Reads $csv for a comma separated list of image attachment IDs. Returns a php array of image URLs and IDs, or false.
 *
 * @param string $csv A comma separated list of image attachment IDs.
 * @return array|bool
 */
function napoleon_featgal_get_images( $csv = '' ) {
	$ids = explode( ',', $csv );
	$ids = array_filter( $ids );

	if ( count( $ids ) > 0 ) {
		$ids         = array_map( 'intval', $ids );
		$ids         = array_map( 'abs', $ids );
		$urls        = array();

		global $_wp_additional_image_sizes;

		$image_sizes = $_wp_additional_image_sizes;

		foreach ( $ids as $id ) {
			$thumb_file = wp_get_attachment_image_url( $id, 'napoleon_featgal_small_thumb' );

			$file = parse_url( $thumb_file );
			$file = pathinfo( $file['path'] );
			$file = basename( $file['basename'], '.' . $file['extension'] );

			$size = $image_sizes['napoleon_featgal_small_thumb']['width'] . 'x' . $image_sizes['napoleon_featgal_small_thumb']['height'];
			if ( substr( $file, - strlen( $size ), strlen( $size ) ) === $size ) {
				$file = $thumb_file;
			} else {
				$file = wp_get_attachment_image_url( $id, 'thumbnail' );
			}

			$data = array(
				'id'  => $id,
				//'url' => wp_get_attachment_image_url( $id, 'napoleon_featgal_small_thumb' )
				'url' => $file,
			);

			$urls[] = $data;
		}
		return $urls;
	} else {
		return false;
	}
}
