# ملفات نموذج الطلب في قالب Napoleon WordPress

## الملفات الأساسية المسؤولة عن نموذج الطلب

### 1. الملف الرئيسي لنموذج الطلب
**المسار:** `inc/order-form/codplugin.php`
- **الوظيفة:** الملف الرئيسي الذي يتحكم في تشغيل نموذج الطلب
- **المسؤوليات:**
  - تحميل ملفات CSS و JavaScript
  - إضافة نموذج الطلب إلى صفحة المنتج
  - معالجة الطلبات عبر AJAX
  - إدارة إعدادات النموذج

### 2. ملف عرض النموذج الأساسي
**المسار:** `inc/order-form/include/core/main.php`
- **الوظيفة:** يحتوي على دالة `codplugin_checkout_form()` التي تعرض نموذج الطلب
- **المسؤوليات:**
  - عرض حقول النموذج (الاسم، رقم الهاتف، المحافظة، العنوان)
  - عرض خيارات المنتج المتغيرة
  - حساب أسعار الشحن
  - عرض ملخص الطلب

### 3. ملفات التنسيق والتصميم
**المسار:** `inc/order-form/include/assets/css/codplugin.css`
- **الوظيفة:** تنسيق وتصميم نموذج الطلب
- **المسؤوليات:**
  - تصميم الحقول والأزرار
  - تنسيق ملخص الطلب
  - تصميم خيارات المنتج المتغيرة

### 4. ملف JavaScript للتفاعل
**المسار:** `inc/order-form/include/assets/js/codplugin.js`
- **الوظيفة:** التفاعل مع النموذج وإرسال البيانات
- **المسؤوليات:**
  - حساب الأسعار الإجمالية
  - إرسال الطلبات عبر AJAX
  - تحديث أسعار الشحن حسب المحافظة
  - التحكم في كمية المنتج

### 5. ملفات WooCommerce المخصصة
**المسار:** `woocommerce/single-product.php`
- **الوظيفة:** قالب صفحة المنتج الفردي
- **المسؤوليات:**
  - عرض صفحة المنتج
  - إضافة زر "اشتري الآن" الثابت

**المسار:** `woocommerce/content-single-product.php`
- **الوظيفة:** محتوى صفحة المنتج
- **المسؤوليات:**
  - عرض تفاصيل المنتج
  - إضافة نموذج الطلب في قسم `#product-buy-form`

### 6. ملفات إضافية مهمة

**المسار:** `inc/order-form/include/core/ajax.php`
- **الوظيفة:** معالجة طلبات AJAX

**المسار:** `inc/order-form/include/core/php-orders.php`
- **الوظيفة:** إنشاء الطلبات في WooCommerce

**المسار:** `inc/order-form/include/core/customizer.php`
- **الوظيفة:** إعدادات النموذج في WordPress Customizer

## العناصر الرئيسية في النموذج

### 1. حقول النموذج
- **الاسم الكامل:** `full_name`
- **رقم الهاتف:** `phone_number`
- **المحافظة:** `codplugin_state`
- **المدينة:** `codplugin_city` (اختياري)
- **العنوان الكامل:** `full_address`
- **ملاحظات الطلب:** `order_notes`
- **البريد الإلكتروني:** `codplugin_email` (اختياري)

### 2. خيارات المنتج
- **المنتجات المتغيرة:** عرض الخيارات مع الأسعار
- **الكمية:** أزرار زيادة ونقصان الكمية
- **السعر:** حساب السعر الإجمالي مع الشحن

### 3. ملخص الطلب
- **اسم المنتج والكمية**
- **سعر الشحن حسب المحافظة**
- **السعر الإجمالي**

### 4. أزرار الإجراءات
- **زر تأكيد الطلب:** `#nrwooconfirm`
- **زر الطلب عبر واتساب:** `#whatsapp-orders`
- **زر إضافة للسلة:** (اختياري)

## المعرفات المهمة (IDs) في الكود

- `#codplugin-checkout` - الحاوي الرئيسي للنموذج
- `#codplugin_woo_single_form` - النموذج الأساسي
- `#product-buy-form` - منطقة عرض النموذج في صفحة المنتج
- `#codplugin_show_hide` - ملخص الطلب
- `#nrwooconfirm` - زر تأكيد الطلب

## ملاحظات مهمة

1. **التحكم في العرض:** يمكن إخفاء/إظهار النموذج من خلال إعداد `display_order_form` في WordPress Customizer
2. **التوافق مع الإضافات:** النموذج يتحقق من وجود إضافات CODPLUGIN أو TASHEEL
3. **الشحن:** يحسب أسعار الشحن تلقائياً حسب المحافظة المختارة
4. **الطلبات:** يمكن إنشاء الطلبات عبر PHP أو AJAX حسب الإعدادات
5. **التخصيص:** جميع النصوص والألوان قابلة للتخصيص من WordPress Customizer