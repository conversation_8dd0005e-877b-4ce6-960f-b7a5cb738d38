<?php
// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class RID_COD_Rest_Api
 * Placeholder for potential future WordPress REST API endpoints.
 * License activation is currently handled via admin-ajax in class-rid-cod-ajax-handler.php
 */
class RID_COD_Rest_Api {

    public function __construct() {
        // Removed AJAX hooks to avoid conflict with class-rid-cod-ajax-handler.php
        // add_action('wp_ajax_ridcode_activate_license', [$this, 'handle_activation_request']);
        // add_action('wp_ajax_ridcode_deactivate_license', [$this, 'handle_deactivation_request']);
    }

    // Removed handle_activation_request() method as it's now in class-rid-cod-ajax-handler.php

    // Removed handle_deactivation_request() method placeholder

} // End class