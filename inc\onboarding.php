<?php
/**
 * napoleon onboarding related code.
 */
add_filter( 'ocdi/plugin_page_setup', 'napoleon_ocdi_plugin_page_setup' );
function napoleon_ocdi_plugin_page_setup( $default_settings ) {
  $default_settings['parent_slug'] = 'themes.php';
  $default_settings['page_title']  = esc_html__( 'One Click Demo Import' , 'napoleon' );
  $default_settings['menu_title']  = esc_html__( 'Import Demo Data' , 'napoleon' );
  $default_settings['capability']  = 'import';

  return $default_settings;
}


add_filter( 'ocdi/timeout_for_downloading_import_file', 'napoleon_ocdi_download_timeout' );
function napoleon_ocdi_download_timeout( $timeout ) {
	return 60;
}


add_filter( 'ocdi/register_plugins', 'napoleon_ocdi_register_plugins' );
function napoleon_ocdi_register_plugins( $plugins ) {
  $theme_plugins = [

    [ // Woocommerce Plugin
      'name'     => __( 'Woocommerce', 'napoleon' ),
      'slug'     => 'woocommerce', 
      'description'=> __( 'Sell anything, beautifully.', 'napoleon'),
      'required' => true,  
                  
    ],


    [ // Elementor Plugin
      'name'     => __( 'Elementor', 'napoleon' ),
      'slug'     => 'elementor',        
   	  'description'=>__('Elementor is a front-end drag & drop page builder for WordPress.','napoleon'),     
	  'required' => false,

    ],
	
    [ // Elementor Pro Plugin
      'name'     => __( 'Elementor Pro', 'napoleon' ),
      'slug'     => 'elementor-pro', 
      'description'        => __( 'Add a fast order form in product pages', 'napoleon' ),
      'required' => false,            
    ],

	[ // JetWooBuilder Plugin
		'name'     => __( 'JetWooBuilder', 'napoleon' ),
		'slug'     => 'jet-woo-builder',      
		'description'=>__('Set of modules for WooCommerce based on Elementor Page Builder.','napoleon'),     
		'required' => false,
  
	  ],
    [ // Maxslider Plugin
      'name'     => __( 'MaxSlider', 'napoleon' ),
      'slug'     => 'maxslider',        
	  'description'=> __('Add a custom responsive slider to any page of your website.', 'napoleon'),     
	  'required' => false,
    ],
    
  ];
 
  return array_merge( $plugins, $theme_plugins );
}


add_filter( 'ocdi/import_files', 'napoleon_ocdi_import_files' );
function napoleon_ocdi_import_files( $files ) {

	$demo_dir_url = untrailingslashit( apply_filters( 'napoleon_ocdi_demo_dir_url', 'https://bitherhood.com/napoleon-demo/napoleon' ) );


	// When having more that one predefined imports, set a preview image, preview URL, and categories for isotope-style filtering.
	return array(
		array(
	      'import_file_name'           => esc_html__( 'Napoleon', 'napoleon' ),
	      'import_file_url'            => $demo_dir_url . '/napoleon-content.xml',
	      'import_widget_file_url'     => $demo_dir_url . '/napoleon-widgets.wie',
	      'import_customizer_file_url' => $demo_dir_url . '/napoleon-customizer.dat',
	      'import_preview_image_url'   => $demo_dir_url . '/napoleon-screenshot.png',
	    ),	
	);
}
// mahdi lazy script
add_action( 'ocdi/after_import', 'napoleon_ocdi_after_import_setup' );
function napoleon_ocdi_after_import_setup() {

    // Set up nav menus.
    $main_menu  = get_term_by( 'name', 'main', 'nav_menu' );
    if ( $main_menu ) {
        set_theme_mod( 'nav_menu_locations', array(
            'menu-1' => $main_menu->term_id,
        ));
    }

    // Set up home and blog pages.
    $front_page_id = get_page_by_path( 'home' );
    if ( $front_page_id ) {
        update_option( 'show_on_front', 'page' );
        update_option( 'page_on_front', $front_page_id->ID );
    }

    // WooCommerce setup.
    if ( class_exists( 'WooCommerce' ) ) {

        // Set default currency to Algerian Dinar (DZD).
        update_option( 'woocommerce_currency', 'DZD' ); // 'DZD' is the currency code for Algerian Dinar

        // Set WooCommerce pages with -2 suffix.
        $wc_shop_page_id      = get_page_by_path( 'shop-2' );
        $wc_cart_page_id      = get_page_by_path( 'cart-2' );
        $wc_checkout_page_id  = get_page_by_path( 'checkout-2' );
        $wc_myaccount_page_id = get_page_by_path( 'my-account-2' );

        if ( $wc_shop_page_id ) {
            update_option( 'woocommerce_shop_page_id', $wc_shop_page_id->ID );
        }
        if ( $wc_cart_page_id ) {
            update_option( 'woocommerce_cart_page_id', $wc_cart_page_id->ID );
        }
        if ( $wc_checkout_page_id ) {
            update_option( 'woocommerce_checkout_page_id', $wc_checkout_page_id->ID );
        }
        if ( $wc_myaccount_page_id ) {
            update_option( 'woocommerce_myaccount_page_id', $wc_myaccount_page_id->ID );
        }

        // Delete WooCommerce pages (without the -2 suffix).
        $pages_to_delete = array( 'shop', 'cart', 'checkout', 'my-account', 'sample-page', 'refund_returns', 'privacy-policy' );

        foreach ( $pages_to_delete as $slug ) {
            $page = get_page_by_path( $slug );
            if ( $page ) {
                wp_delete_post( $page->ID, true ); // true for force delete
                error_log( "Deleted page: {$slug}" );
            }
        }
    }
}

function napoleon_get_theme_recommended_plugins() {
	return apply_filters( 'napoleon_theme_recommended_plugins', array(

		'woocommerce'           => array(
			'title'              => __( 'WooCommerce', 'napoleon' ),
			'description'        => __( 'Sell anything, beautifully.', 'napoleon' ),
			'required_by_sample' => true,
		),
		'maxslider'             => array(
			'title'              => __( 'MaxSlider', 'napoleon' ),
			'description'        => __( 'Add a custom responsive slider to any page of your website.', 'napoleon' ),
			'required_by_sample' => false,
		),
		'elementor'             => array(
			'title'              => __( 'Elementor', 'napoleon' ),
			'description'        => __( 'Elementor is a front-end drag & drop page builder for WordPress.', 'napoleon' ),
			'required_by_sample' => true,
		),
		'elementor-pro'             => array(
			'title'              => __( 'Elementor Pro', 'napoleon' ),
			'description'        => __( 'Elevate your designs and unlock the full power of Elementor Pro.', 'napoleon' ),
			'bundled'   => true,
			'required_by_sample' => true,
		),		
		'jet-woo-builder'             => array(
			'title'              => __( 'JetWooBuilder', 'napoleon' ),
			'description'        => __( 'Set of modules for WooCommerce based on Elementor Page Builder.', 'napoleon' ),
			'bundled'   => true,
			'required_by_sample' => true,
		),	
		'one-click-demo-import' => array(
			'title'              => __( 'One Click Demo Import', 'napoleon' ),
			'description'        => __( 'Import your demo content, widgets and theme settings with one click.', 'napoleon' ),
			'required_by_sample' => true,
		)
	) );
}

add_action( 'init', 'napoleon_onboarding_page_init' );

function napoleon_onboarding_page_init() {

	$data = array(
		'show_page'                => true,
		'description'              => __( 'napoleon is a powerful e-commerce theme for WordPress.', 'napoleon' ),
		'recommended_plugins_page' => array(
			'plugins' => napoleon_get_theme_recommended_plugins(),
		),
	);

	$onboarding = new napoleon_Onboarding_Page();
	$onboarding->init( apply_filters( 'napoleon_onboarding_page_array', $data ) );
}


/**
 * User onboarding.
 */
require_once get_theme_file_path( '/inc/onboarding/onboarding-page.php' );
