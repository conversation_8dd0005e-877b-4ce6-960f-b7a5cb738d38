<?php

/**
 * Check if WooCommerce is activated
 */


include_once( ABSPATH . 'wp-admin/includes/plugin.php' );
if ( !is_plugin_active( 'woocommerce/woocommerce.php') ) {
    return;
}
// Check if CODPLUGIN or TASHEEL are active
if ( is_plugin_active( 'codplugin/codplugin.php') || is_plugin_active( 'tasheel/tasheel.php') ) {
    return;
}



 if ( get_theme_mod( 'block_desktop_visitors', 0 ) == 1 && ! current_user_can( 'manage_options' ) && ! wp_is_mobile()  ) { 
    return;
 } 

if ( ! get_theme_mod( 'display_order_form', 1 ) ) {
    return;
}



/**
 * Load core files
 */
if ( art_is_theme_license_valid() ) {
    require_once get_theme_file_path( '/inc/order-form/include/core/customizer.php' );
}
require_once get_theme_file_path( '/inc/order-form/include/core/main.php' );
require_once get_theme_file_path( '/inc/order-form/include/core/php-orders.php' );
require_once get_theme_file_path( '/inc/order-form/include/core/ajax.php' );
require_once get_theme_file_path( '/inc/order-form/include/abandoned-carts/abandoned-carts.php' );



/**
 * Display COD form in elementor 
 * 
 */

function register_cod_checkout_form_widget( $widgets_manager ) {
    require_once get_theme_file_path( '/inc/order-form/include/core/codplugin-elementor.php' );

    $widgets_manager->register( new \COD_Plugin_Checkout_Form_Widget() );
}
add_action( 'elementor/widgets/register', 'register_cod_checkout_form_widget' );


function codplugin_woo_single_product_order(){
    if ( is_product() || is_page() ) {
        wp_enqueue_style(
            "codplugin_woo_style",
            get_template_directory_uri() . "/inc/order-form/include/assets/css/codplugin.css",
            array(),
            '3.0'
        );

    
        wp_enqueue_script("jquery");
        wp_enqueue_script(
            "codplugin_woo_script",
            get_template_directory_uri() . "/inc/order-form/include/assets/js/codplugin.js",
            [],
            "3.0",
            true
        );

        wp_enqueue_script(
            "codplugin_confetti_script",
            get_template_directory_uri() . "/inc/order-form/include/assets/js/confetti.browser.min.js",
            [],
            "1.0",
            true
        );

        wp_localize_script("codplugin_woo_script", "codplugin_order", [
            "ajax_url" => admin_url("admin-ajax.php"),
            "variable_message" => __('Please Select Product Type', 'napoleon'),
            "processing_message" => __('Processing...', 'napoleon'),
            "completed_message" => __('Order was successfully submitted! ', 'napoleon'),
            "error_message" => __('An error occurred!', 'napoleon'),
        ]);

        if ( get_theme_mod( 'autocomplete_state_list', 0)  == 1 ) {
            wp_enqueue_style(
                "codplugin_chosen_css",
                get_template_directory_uri() . "/inc/order-form/include/assets/chosen/chosen.min.css"
            );
            wp_enqueue_script("codplugin_chosen_js", get_template_directory_uri() . "/inc/order-form/include/assets/chosen/chosen.jquery.min.js", [], "1.8.7", true );
        }
    }


}
add_action( 'wp_enqueue_scripts', 'codplugin_woo_single_product_order' );




if(!function_exists('codplugin_woocommerce_template_single_add_to_cart')):
    add_action("init", function () {
        add_action(
            "woocommerce_single_product_summary",
            "codplugin_woocommerce_template_single_add_to_cart",
            30
        );
        
    });

    function codplugin_woocommerce_template_single_add_to_cart() {
        global $product;
        codplugin_checkout_form($product->get_id());
    }
endif;



add_action( 'woocommerce_thankyou', 'woocommerce_thankyou_change_order_status', 10, 1 );
function woocommerce_thankyou_change_order_status( $order_id ){
    if( ! $order_id ) return;

    $order = wc_get_order( $order_id );

    if( $order->get_status() == 'pending' )
        $order->update_status( 'processing' );

    ?>
        <script> jQuery(document).ready(function($) {
            launchConfetti();
            function launchConfetti() {
                var duration = 1 * 1000;
                var end = Date.now() + duration;

                (function frame() {
                  // launch a few confetti from the left edge
                  confetti({
                    particleCount: 7,
                    angle: 60,
                    spread: 55,
                    origin: { x: 0 }
                  });
                  // and launch a few from the right edge
                  confetti({
                    particleCount: 7,
                    angle: 120,
                    spread: 55,
                    origin: { x: 1 }
                  });

                  // keep going until we are out of time
                  if (Date.now() < end) {
                    requestAnimationFrame(frame);
                  }
                })();
            }
        });
        </script>
    <?php
}



/**
 * Add custom inline css .
 */

function codplugin_inline_style() { 

    if ( get_theme_mod( 'display_order_summary', 1)  == 1 ) :  ?>

        <style>
            #codplugin_show_hide {display: block;}

        </style>
    
    <?php else: ?>
        <style>
            #codplugin_show_hide {display: none;}

        </style>

    <?php endif;

    if ( get_theme_mod( 'animate_order_btn', 0)  == 1 ) :  ?>
    
        <style>
            #nrwooconfirm:not(.atc-buy-button) {
              animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
              transform: translate3d(0, 0, 0);
              perspective: 1000px;
            }
           
        </style>
    <?php endif;

}
add_action( 'wp_head', 'codplugin_inline_style' );



/**
 * Create custom shortcode for cod form 
 */
function codplugin_shortcode( $atts ) {
    $product_id =  $atts['id'] ;
    codplugin_checkout_form($product_id);
}
add_shortcode( 'codform', 'codplugin_shortcode' );




/**
 * Add States and Places To COD Form 
 */

add_action('codplugin_state_city', 'my_custom_checkout_fields');
function my_custom_checkout_fields(){
    global $woocommerce;
    $countries_obj = new WC_Countries();
    $countries = $countries_obj->__get("countries");
    $default_country = $countries_obj->get_base_country();

    include(get_theme_file_path() . '/inc/order-form/include/states/' . $default_country . '.php');
    include(get_theme_file_path() . '/inc/order-form/include/places/' . $default_country . '.php');

    $states = $states[$default_country];
    $cities = $places[$default_country];

    // Create state options array with unique IDs
    $state_options = array('' => get_theme_mod( 'form_state', __('State', 'napoleon' ) ) ) ;
    foreach ($states as $state_id => $state_name) {
        $state_options[$state_id] = $state_name;
    }

    woocommerce_form_field( 'codplugin_state', array(
        'type'          => 'select',
        'name'          => 'codplugin_state',
        'class'         => array('codplugin-field'),
        'placeholder'   => get_theme_mod( 'form_state', __('State', 'napoleon' ) ),
        'required'      => true,
        'options'       => $state_options,
    ));
              
    // Create city options array with unique IDs
    $city_options = array('' => get_theme_mod( 'form_city', __('City', 'napoleon' ) ) ) ;

    woocommerce_form_field( 'codplugin_city', array(
        'type'          => 'select',
        'class'         => array('codplugin-field'),
        'placeholder'   => get_theme_mod( 'form_city', __('City', 'napoleon' ) ) ,
        'required'      => true,
        'options'       => $city_options,
    ) );

    // Add JavaScript to dynamically update city field options based on the selected state
    ?>
            
    <script type="text/javascript">
        jQuery(document).ready(function($){
            var cities = <?php echo json_encode($cities); ?>;
            
            // Disable the first option of the state select element
            $('select#codplugin_state option:first').attr('disabled', true);

            $('select#codplugin_state').change(function(){
                var states = $(this).val();

                var city_options = '';
                $.each(cities[states], function(city_id, city_name){
                    city_options += '<option value="' + city_id + '">' + city_name + '</option>';
                });
                $('select#codplugin_city').html('<option value="">' + '<?php echo get_theme_mod( 'form_city', __('City', 'napoleon' ) ); ?>' + '</option>' + city_options);


            });
        });
    </script>
<?php 
}


function napoleon_random_products_on_thankyou_page() {

     if ( get_theme_mod( 'thanks_related_products', 0)  == 0) {
        return;
     }
    // Query for random products from the shop
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => 15,
        'orderby' => 'rand', // Order by random
    );

    $random_query = new WP_Query( $args );

    if ( $random_query->have_posts() ) {
        echo '<div class="section-heading"><h3 class="section-title">' . __( 'You may also like&hellip;', 'napoleon' ) .'</h3></div>';
        echo '<div class="thanks-products">';

        while ( $random_query->have_posts() ) : $random_query->the_post();
            wc_get_template_part( 'content', 'product' );
        endwhile;

        echo '</div>';

        wp_reset_postdata();
    }
}

add_action( 'woocommerce_thankyou', 'napoleon_random_products_on_thankyou_page', 10 );

function add_whatsapp_order_button() { 
    

    if (  get_theme_mod( 'whatsapp_number' )  ) : ?>

        <script>

        jQuery(document).ready(function($) {
          // WhatsApp button click event
          $('#whatsapp-orders').on('click', function() {
            // Get the product URL and product name dynamically using WooCommerce functions
            var whatsappPhoneNumber = '<?php echo get_theme_mod( 'whatsapp_number' ); ?>';
            var productURL = '<?php echo get_permalink(); ?>';
            var productName = '<?php echo get_the_title(); ?>';

            // Customize your WhatsApp message
            var message = '<?php _e('Hi, I am interested in this product: ','napoleon'); ?>' + productName + '\n' + productURL;

            // Encode the message for WhatsApp
            var encodedMessage = encodeURIComponent(message);

            // WhatsApp API link
            var whatsappURL = 'https://api.whatsapp.com/send?phone=' + whatsappPhoneNumber + '&text=' + encodedMessage;

            // Redirect to WhatsApp
            window.location.href = whatsappURL;
          });
        });
        </script> 
        
        <?php endif; 

}

add_action( 'wp_footer', 'add_whatsapp_order_button' );


// Footer Scripts
function codplugin_footer_scripts() {  
        

        $order_currency = get_woocommerce_currency();

        ?>

        <script>
      

        // Add alert to select a variation before click add to cart
        jQuery(document).ready(function($) {

            function launchConfetti() {
                var duration = 1 * 1000;
                var end = Date.now() + duration;

                (function frame() {
                  // launch a few confetti from the left edge
                  confetti({
                    particleCount: 7,
                    angle: 60,
                    spread: 55,
                    origin: { x: 0 }
                  });
                  // and launch a few from the right edge
                  confetti({
                    particleCount: 7,
                    angle: 120,
                    spread: 55,
                    origin: { x: 1 }
                  });

                  // keep going until we are out of time
                  if (Date.now() < end) {
                    requestAnimationFrame(frame);
                  }
                })();
            }
                       

            var variationId = $('#var_id').val();
            var $productLink = $("a.custom-atc-btn.product_type_variable:not(.add_to_cart_button)");


            if (variationId === '') {
                $("a.custom-atc-btn.product_type_variable").attr("href", "");
                $("a.custom-atc-btn.product_type_variable").removeClass("add_to_cart_button popup-alert");

            } else {
                $("a.custom-atc-btn.product_type_variable").attr("href", "?add-to-cart=" + variationId);
                $("a.custom-atc-btn.product_type_variable").attr("data-product_id",variationId);
                $("a.custom-atc-btn.product_type_variable").addClass("add_to_cart_button popup-alert");
            }

             // Event delegation to handle click event
            $(document).on('click', 'a.custom-atc-btn.product_type_variable:not(.popup-alert)', function(e) {
                    e.preventDefault();
                    alert("<?php _e('Please select a product variation', 'napoleon'); ?>");
            });



            // Form Submission
 
            <?php if ( get_theme_mod( 'create_orders_with_php', 0)  == 1 ) : ?>
                $("#codplugin_woo_single_form").on("submit", function(e) {

                    if($('input.variation_id').val() == 0 ) {
                        e.preventDefault();
                        alert(codplugin_order.variable_message);
                        return;
                    }
                    $("#nrwooconfirm").attr('disabled', true);

                    $('#codplugin_state option:selected').val(function(){
                       return $(this).text();
                    });
                    $('#codplugin_city option:selected').val(function(){
                       return $(this).text();
                    });

                   $("#nrwooconfirm:not(.atc-buy-button)").val(codplugin_order.processing_message);

           
                    $("#codplugin_gif").css('display', 'block');
                    setTimeout(() => {
                        $("#codplugin_gif").css('display', 'none');
                    }, 500);
            
                });

            <?php else : ?>

                var codplugin_thankyou_url = $("#codplugin_thankyou_url").val();

                $("#codplugin_woo_single_form").on("submit", function(e) {
                    e.preventDefault();


                    if($('input.variation_id').val() == 0 ) {
                        alert(codplugin_order.variable_message);
                        return;
                    }

                    $("#nrwooconfirm").attr('disabled', true);

                    $("#codplugin_gif").css('display', 'block');
                    setTimeout(() => {
                        $("#codplugin_gif").css('display', 'none');
                    }, 500);



                    $("#nrwooconfirm:not(.atc-buy-button)").val(codplugin_order.processing_message);

                    
                    $('#codplugin-processing').show();
                    

                    $('#codplugin_state option:selected').val(function(){
                       return $(this).text();
                    });
                    $('#codplugin_city option:selected').val(function(){
                       return $(this).text();
                    });

                     if ($("#thanks-order-summary").length > 0) {
                        $(' .main, .footer').hide();
                        $( ".head-mast" ).addClass( "thanks-active" );
                        $('html, body').animate({ scrollTop: 0 }, 'slow');

                        $('#codplugin-thanks').show();
                        $('#codplugin_show_hide').clone().appendTo('#thanks-order-summary');
                        launchConfetti();

                        var order_total = $("#codplugin_total_price").text()

                        if (typeof fbq !== 'undefined') {
                            fbq('track', 'Purchase', {
                                value: order_total,
                                currency: '<?php echo $order_currency; ?>',
                                content_type: 'product'
                            });
                        }
                    }

        
                    var formdata = new FormData(this);
                    formdata.append("action", "codplugin_order_form_action");
                    

                    $.ajax({
                        url: codplugin_order.ajax_url,
                        action: "codplugin_order_form_action",
                        type: "post",
                        data: formdata,
                        contentType: false,
                        processData: false,
                        success: function(val) {
                            
                            var obj = $.parseJSON(val);
                            orderID = obj.order_id;
                            orderKey = obj.order_key;

                           if($("#cod-upsell").length == 0 && $("#thanks-order-summary").length == 0) { // there is no upsell, no fast thank you
                                window.location.href=codplugin_thankyou_url+'order-received/'+orderID+'/?key='+orderKey;
                            }else{
                                $("#cod-upsell").show();
                            }
                        },
                        error: function(val){
                            console.log(val);
                        }

                    });
                });

                $("#cod-upsell-cancel").click(function() {
                    window.location.href=codplugin_thankyou_url+'order-received/'+orderID+'/?key='+orderKey;
                });

                $("#cod-add-upsell").click(function() {
                    productID = $("#upsell_product_id").val();
                    $("#cod-add-upsell").val(codplugin_order.completed_message);
                    $("#cod-add-upsell").attr('disabled', true);
                    setTimeout(() => {
                        $("#cod-add-upsell").attr('disabled', false);
                        $("#cod-add-upsell").val(codplugin_order.error_message);
                    }, 8000);
                    $("#cod-upsell-loader").css('display', 'block');
                    setTimeout(() => {
                        $("#cod-upsell-loader").css('display', 'none');
                    }, 8000);
                    
                    var data = {
                        action : "codplugin_add_upsell_product",
                        order_id : orderID,
                        product_id : productID,
                    };
                    $.ajax({
                        url: codplugin_order.ajax_url,
                        type: "post",
                        data : data,
                        success: function(val) {
                            console.log(val);
                            window.location.href=codplugin_thankyou_url+'order-received/'+orderID+'/?key='+orderKey;
                        },
                        error: function(error){
                            console.log(error);
                        },

                    });
                    
                });

            <?php endif; ?>

        });
        </script>         
   <?php 

}
add_action( 'wp_footer', 'codplugin_footer_scripts' );



function napoleon_fast_thankyou() { 
    if ( get_theme_mod('enable_fast_thanks', 0) == 1 ) : ?>
        
        <div id="codplugin-thanks">
            <div id="codplugin-thanks-box">           
                <p class="thanks-box-title blink-me"><?php _e('Your order has been placed', 'napoleon'); ?></p>
                <div class="thanks-box-content"><?php echo wp_kses_post(get_theme_mod('thanks_editor')); ?></div>
                <div id="thanks-order-summary"> 
                    <div class="order-summary-title">
                        <i  class="fas fa-shopping-cart " ></i>
                        <?php _e('Order Summary', 'napoleon'); ?>
                    </div> 
                </div>
            </div>
        </div>

    <?php 
    endif;
}

add_action( 'wp_footer', 'napoleon_fast_thankyou' );
