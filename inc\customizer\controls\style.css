

body .select2-container {
  z-index: 1000000;
}

.customize-control .select2-container {
  z-index: 1;
}

.customize-control .select2-container .select2-selection--single {
  height: 32px;
}

.customize-control .select2-container--default .select2-selection--single {
  height: 32px;
  border-color: #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.customize-control .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #32373c;
  line-height: 30px;
}

.customize-control .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 30px;
}

.customize-control .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #0073aa;
}

.customize-control .select2-search--dropdown .select2-search__field {
  border-color: #ddd;
  border-radius: 4px;
}

.napoleon-responsive-controls-tablet,
.napoleon-responsive-controls-mobile {
  display: none;
}

.button-group-devices {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
}

.button-group-devices button {
  cursor: pointer;
  background: 0 0;
  border: none;
  height: 26px;
  padding: 0 1px;
  margin: 0;
  box-shadow: none;
  border: 0;
}

.button-group-devices button:focus,
.button-group-devices button:active {
  box-shadow: none;
}

.button-group-devices button::before {
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  font: normal 15px/17px dashicons;
  vertical-align: top;
  margin: 0 0;
  padding: 2px 4px;
  color: #656a6f;
}

.button-group-devices .active::before {
  color: #3498d8;
}

.button-group-devices .preview-desktop::before {
  content: "\f472";
}

.button-group-devices .preview-tablet::before {
  content: "\f471";
}

.button-group-devices .preview-mobile::before {
  content: "\f470";
}