.group:after {
	content: "";
	display: table;
	clear: both;
}

.at-media-manager-gallery .at-upload-to-gallery-preview {
	margin: 10px 0px;
	padding: 5px 0px;
	border: 1px solid #DFDFDF;
	border-radius: 3px 3px 3px 3px;
}

.at-upload-to-gallery-preview-text {
	font-size: 26px;
	color: #ccc;
}

.thumb + .at-upload-to-gallery-preview-text {
	display: none;
}

.at-media-manager-gallery .thumb {
	margin: 5px;
	padding: 3px;
	border: 1px solid #DFDFDF;
	border-radius: 3px 3px 3px 3px;
	float: left;
	width: 100px;
	height: 100px;
	position: relative;
	overflow: hidden;
}

.at-media-manager-gallery .thumb img {
	max-width: 100px;
}

.at-media-manager-gallery .thumb .close {
	background-color: #FFFFFF;
	background-position: -96px 4px;
	border-radius: 3px 3px 3px 3px;
	border-width: 0;
	box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
	color: #464646;
	display: none;
	font-size: 20px;
	line-height: 20px;
	padding: 0;
	position: absolute;
	right: 5px;
	text-align: center;
	text-decoration: none;
	top: 5px;
	height: 21px;
	width: 21px;
}
.at-media-manager-gallery .thumb:hover .close {
	display: block;
}
.at-media-manager-gallery .at-upload-to-gallery-preview p {
	margin: 5px;
	text-align: center;
}


.at-upload-preview .upload-preview {
	display: block;
	max-width: 100px;
	position: relative;
}

.at-upload-preview .upload-preview img {
	border-radius: 3px 3px 3px 3px;
	width: 100px;
	height: 100px;
	position: relative;
	overflow: hidden;
}

.at-upload-preview .upload-preview .close {
	background-color: #FFFFFF;
	background-position: -96px 4px;
	border-radius: 3px 3px 3px 3px;
	border-width: 0;
	box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
	color: #464646;
	display: none;
	font-size: 20px;
	line-height: 20px;
	padding: 0;
	position: absolute;
	right: 5px;
	text-align: center;
	text-decoration: none;
	top: 5px;
	height: 21px;
	width: 21px;
}
.at-upload-preview .upload-preview:hover .close {
	display: block;
}

/* Custom field wrappers and tabs */
.at-cf-wrap { margin-top:12px; }
.at-cf-section { clear:both; border:solid 1px #e4e4e4; padding:20px; border-radius:2px; position: relative; z-index: 1;}
.at-cf-section *+* { margin-bottom: 0; }
.at-cf-section input[type='text']:not(.wp-color-picker),
.at-cf-section select { min-width: 200px; }
.at-cf-section .at-field-group { margin: 0 0 15px 0; }
.at-cf-section label { display: block; margin-bottom: 5px; }
.at-cf-section .at-field-radio label, .at-cf-section .at-field-checkbox label { display: inline-block; margin: 0; }
.at-cf-section textarea { min-height: 100px; }
.at-cf-inside :last-child { margin-bottom: 0; }
.at-cf-guide { background: #fffce6; color: #948832; font-size: 12px; border: solid 1px #eeeac9; padding:15px; margin:0 0 15px 0; }
ol.at-cf-guide { list-style-position: inside; }
.at-cf-guide code {  background: #f8e19d; border-radius:2px; border:solid 1px #e5cd84; color: #aa5827; }
.at-cf-guide em { font-family: Consolas,Monaco,monospace; font-style: normal; display:inline-block; padding: 0 3px; margin: 0 1px; border-radius:2px; background: #f1f1f1; color:#fff; border:solid 1px #e6e6e6; color: #333; }
.at-cf-title { display: none; margin:0 0 10px 0 !important; padding: 0 !important; }
.at-cf-tabs { margin:0; position:relative; z-index: 2; }
.at-cf-tab { display: inline-block; margin:0 -1px 0 0; padding:14px 12px 12px 12px; position:relative; top: 1px; z-index: 4; border:solid 1px #e4e4e4; background:#f2f2f2; color:#7e7e7e; cursor:pointer; border-radius:2px;}
.at-cf-tab:hover { background: #f8f8f8 ;}
.at-cf-tab-active, .at-cf-tab-active:hover { border-top: solid 3px #0073AA; border-bottom: solid 1px #fff; background:#fff; color:#23282d; padding: 12px;}
.at-cf-wrap .wp-picker-container { display: block; margin-top: 3px; }
.at-cf-wrap .alpha-color-picker-wrap .wp-picker-container { display: inline-block; }
.wp-picker-input-wrap label { display: inline-block; }



