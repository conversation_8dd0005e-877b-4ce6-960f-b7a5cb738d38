/* global jQuery, ridcodeLicenseData, wp */
jQuery(document).ready(function($) {
    'use strict';

    const $button = $('#ridcode_activate_license');
    const $deactivateButton = $('#ridcode_deactivate_license'); // Added for potential deactivation
    const $spinner = $button.siblings('.spinner');
    const $messageDiv = $('#ridcode_license_message');
    const $licenseKeyInput = $('#ridcode_license_key');

    // --- Activation --- 
    $button.on('click', function(e) {
        e.preventDefault();

        const licenseKey = $licenseKeyInput.val().trim();

        if (!licenseKey) {
            $messageDiv.html('<div class="notice notice-error inline"><p>' + ridcodeLicenseData.invalid_key + '</p></div>');
            return;
        }

        // Disable button and show spinner
        $button.prop('disabled', true);
        $spinner.addClass('is-active');
        $messageDiv.html('<div class="notice notice-warning inline"><p>' + ridcodeLicenseData.activating_text + '</p></div>');

        $.ajax({
            url: ridcodeLicenseData.ajax_url,
            type: 'POST',
            data: {
                action: ridcodeLicenseData.activate_action,
                _ajax_nonce: ridcodeLicenseData.nonce, // Correct nonce key for admin-ajax
                license_key: licenseKey
            },
            success: function(response) {
                if (response.success) {
                    $messageDiv.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                    // Update UI to show active status and potentially reload or change button
                    // Example: Reload page to reflect changes
                    location.reload(); 
                } else {
                    let errorMessage = response.data && response.data.message ? response.data.message : ridcodeLicenseData.activation_failed;
                    $messageDiv.html('<div class="notice notice-error inline"><p>' + errorMessage + '</p></div>');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                $messageDiv.html('<div class="notice notice-error inline"><p>AJAX Error: ' + textStatus + ' - ' + errorThrown + '</p></div>');
            },
            complete: function() {
                // Re-enable button and hide spinner (only if activation failed, otherwise page reloads)
                 if (!$messageDiv.find('.notice-success').length) {
                    $button.prop('disabled', false);
                    $spinner.removeClass('is-active');
                 }
            }
        });
    });

    // --- Deactivation (Placeholder) ---
    /*
    $deactivateButton.on('click', function(e) {
        e.preventDefault();
        // Add similar AJAX call using ridcodeLicenseData.deactivate_action
        // ...
    });
    */

});