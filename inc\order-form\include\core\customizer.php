<?php
/**
 * Standard Customizer Sections and Settings
 */
add_action( 'customize_register', 'codplugin_customize_register' );

function codplugin_customize_register( $wp_customize ) {

	$wp_customize->add_section(
		'upsells_settings',
		array(
			'title'       => esc_html__( 'Upsells Settings', 'napoleon' ),
			'panel'       => 'woocommerce',
			'description' => esc_html__( 'After checkout upsell settings', 'napoleon' ),
			'priority'    => 10,
		)
	);

	$wp_customize->add_setting( 'show_upsells', array(
		'default'           => false,
		'capability'        => 'edit_theme_options',
		// 'type'				=> 'option',
		// 'sanitize_callback' => 'sanitize_text_field',
	) );
		$wp_customize->add_control( 'show_upsells', array(
			'label'	  => __( 'Show checkout upsell products', 'napoleon' ),
			'section' => 'upsells_settings',
			'type'    => 'checkbox',
		) );

	$wp_customize->add_setting( 'upsell_title', array(
		'default'           => __( 'Wait! Your order is not completed!', 'napoleon' ),
		'capability'        => 'edit_theme_options',
		'sanitize_callback' => 'sanitize_text_field',
	) );
		$wp_customize->add_control( 'upsell_title', array(
			'label'	  => __( 'Checkout Upsell Section Title', 'napoleon' ),
			'section' => 'upsells_settings',
			'type'    => 'text',
		) );

	$wp_customize->add_panel(
		'plugin_settings',
		array(
			'title'    => esc_html__( 'Order Form Settings', 'napoleon' ),
			//'priority' => 1000,
		)
	);

	$wp_customize->add_section(
		'plugin_titles',
		array(
			'title'       => esc_html__( 'Titles', 'napoleon' ),
			'panel'       => 'plugin_settings',
			'description' => esc_html__( 'Customize default texts of order form', 'napoleon' ),
			'priority'    => 10,
		)
	);
		$wp_customize->add_setting( 'add_info', array(
			'default'           => '',
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'add_info', array(
				'label'	  => __( 'Add Information Title', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );


			

		$wp_customize->add_setting( 'form_full_name', array(
			'default'           => esc_html__( 'Full Name', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_full_name', array(
				'label'	  => __( 'Full Name', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );

		$wp_customize->add_setting( 'form_phone', array(
			'default'           => esc_html__( 'Phone Number', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_phone', array(
				'label'	  => __( 'Phone Number', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );


		$wp_customize->add_setting( 'form_state', array(
			'default'           => esc_html__( 'State', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_state', array(
				'label'	  => __( 'State', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );


		$wp_customize->add_setting( 'form_city', array(
			'default'           => esc_html__( 'City', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_city', array(
				'label'	  => __( 'City', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );

		$wp_customize->add_setting( 'form_address', array(
			'default'           => esc_html__( 'Full Address', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_address', array(
				'label'	  => __( 'Address', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );
			// edit placehold order note
			$wp_customize->add_setting( 'form_order_notes', array(
			'default'           => esc_html__( 'Add Note', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_order_notes', array(
				'label'	  => __( 'Order Note', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );
		$wp_customize->add_setting( 'form_email', array(
			'default'           => esc_html__( 'Email', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_email', array(
				'label'	  => __( 'Email', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );

		$wp_customize->add_setting( 'form_button', array(
			'default'           => esc_html__( 'Click Here to Confirm Order', 'napoleon' ),
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => 'sanitize_text_field',
		) );
			$wp_customize->add_control( 'form_button', array(
				'label'	  => __( 'Checkout Button', 'napoleon' ),
				'section' => 'plugin_titles',
				'type'    => 'text',
			) );

	$wp_customize->add_section(
		'plugin_colors',
		array(
			'title'       => esc_html__( 'Colors', 'napoleon' ),
			'panel'       => 'plugin_settings',
			'description' => esc_html__( 'Customize default colors of order form', 'napoleon' ),
			'priority'    => 20,
		)
	);

		$wp_customize->add_setting( 'enable_variation_styling', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'enable_variation_styling', array(
				'type'    => 'checkbox',
				'section' => 'plugin_colors',
				'label'   => esc_html__( 'Enable variations styling', 'napoleon' ),
			) );

		$wp_customize->add_setting( 'accent_color', array(
				'default'    => '#259bea',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'accent_color', array(
				'label'		=> __( 'Accent Color', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'accent_color',
			) ) );

		$wp_customize->add_setting( 'secondary_color', array(
				'default'    => '#bce0f7',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'secondary_color', array(
				'label'		=> __( 'Secondary Color', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'secondary_color',
			) ) );

		$wp_customize->add_setting( 'order_summary_background', array(
				'default'    => '#f0f9ff',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'order_summary_background', array(
				'label'		=> __( 'Order Summary Background', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'order_summary_background',
			) ) );


		$wp_customize->add_setting( 'button_color', array(
				'default'    => '#259bea',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'button_color', array(
				'label'		=> __( 'Button Color', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'button_color',
			) ) );

		
        $wp_customize->add_setting( 'form_background_color', array(
				'default'    => '#ffffff',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'form_background_color', array(
				'label'		=> __( 'Background Color', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'form_background_color',
			) ) );

		$wp_customize->add_setting( 'text_color', array(
				'default'    => '#404040',
				'transport'  => 'refresh',
			) );

			$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'text_color', array(
				'label'		=> __( 'Text Color', 'napoleon' ),
				'section'	=> 'plugin_colors',
				'settings'	=> 'text_color',
			) ) );
	
	$wp_customize->add_section(
		'plugin_options',
		array(
			'title'       => esc_html__( 'Options', 'napoleon' ),
			'panel'       => 'plugin_settings',
		)
	);

		$wp_customize->add_setting( 'enable_abandoned_carts', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'enable_abandoned_carts', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Save abandoned orders', 'napoleon' ),
			) );


		$wp_customize->add_setting( 'autocomplete_state_list', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'autocomplete_state_list', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Autocomplete states list ', 'napoleon' ),
			) );


		$wp_customize->add_setting( 'display_city_field', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'display_city_field', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Display city field in form', 'napoleon' ),
			) );

		$wp_customize->add_setting( 'hide_address_field', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'hide_address_field', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Hide address field in order form ', 'napoleon' ),
			) );

		$wp_customize->add_setting( 'hide_email_field', array(
			'default'           => 1,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'hide_email_field', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Hide email field in order form ', 'napoleon' ),
			) );

		$wp_customize->add_setting( 'display_atc_button', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'display_atc_button', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Display both Add to cart and buy buttons', 'napoleon' ),
			) );

        // Order Note 
	
		$wp_customize->add_setting( 'hide_order_notes', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
		
			$wp_customize->add_control( 'hide_order_notes', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Show order note field in order form', 'napoleon' ),
			) );
		$wp_customize->add_setting( 'display_atc_button', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'display_atc_button', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Display both Add to cart and buy buttons', 'napoleon' ),
			) );
			
		$wp_customize->add_setting( 'display_order_summary', array(
			'default'           => 1,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'display_order_summary', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Keep order summary open in order form ', 'napoleon' ),
			) );



		$wp_customize->add_setting( 'enable_latin_state_name', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'enable_latin_state_name', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Change state name to latin in orders ', 'napoleon' ),
			) );


		$wp_customize->add_setting( 'create_orders_with_php', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'create_orders_with_php', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Create orders with PHP instead of jQuery', 'napoleon' ),
			) );

		$wp_customize->add_setting( 'animate_order_btn', array(
			'default'           => 0,
			'sanitize_callback' => 'absint',
		) );
			$wp_customize->add_control( 'animate_order_btn', array(
				'type'    => 'checkbox',
				'section' => 'plugin_options',
				'label'   => esc_html__( 'Animate order button', 'napoleon' ),
			) );



		$wp_customize->add_section(
			'plugin_thanks',
			array(
				'title'       => esc_html__( 'Thank You Page', 'napoleon' ),
				'panel'       => 'plugin_settings',
			)
		);

			$wp_customize->add_setting( 'thanks_related_products', array(
				'default'           => false,
				'capability'        => 'edit_theme_options',
			) );
				$wp_customize->add_control( 'thanks_related_products', array(
					'label'	  => __( 'Display related products in thank you page', 'napoleon' ),
					'section' => 'plugin_thanks',
					'type'    => 'checkbox',
				) );

			$wp_customize->add_setting( 'enable_fast_thanks', array(
				'default'           => false,
				'capability'        => 'edit_theme_options',
			) );
				$wp_customize->add_control( 'enable_fast_thanks', array(
					'label'	  => __( 'Enable fast thanks page', 'napoleon' ),
					'section' => 'plugin_thanks',
					'type'    => 'checkbox',
				) );


		    $wp_customize->add_setting( 'thanks_editor', array(
		        'default'           => '',
		        'sanitize_callback' => 'wp_kses_post',
		        'transport'         => 'postMessage',
		    ));

		    // Add a new control
		    $wp_customize->add_control( new WP_Customize_Control( $wp_customize, 'thanks_control', array(
		        'label'    => __( 'Thanks Page Content', 'napoleon' ),
		        'section'  => 'plugin_thanks',
		        'settings' => 'thanks_editor',
		        'type'     => 'textarea',
		    )));


		$wp_customize->add_section(
			'plugin_whatsapp',
			array(
				'title'       => esc_html__( 'Whatsapp Orders', 'napoleon' ),
				'panel'       => 'plugin_settings',
			)
		);

			$wp_customize->add_setting( 'whatsapp_number', array(
				'default'           => '',
				'capability'        => 'edit_theme_options',
				'sanitize_callback' => 'sanitize_text_field',
			) );
				$wp_customize->add_control( 'whatsapp_number', array(
					'label'	  => __( 'Add Your Whatsapp Number', 'napoleon' ),
					'section' => 'plugin_whatsapp',
					'type'    => 'text',
				) );

			$wp_customize->add_setting( 'whatsapp_text', array(
				'default'           => __('Order from Whatsapp','napoleon'),
				'capability'        => 'edit_theme_options',
				'sanitize_callback' => 'sanitize_text_field',
			) );
				$wp_customize->add_control( 'whatsapp_text', array(
					'label'	  => __( 'Add Whatsapp button text', 'napoleon' ),
					'section' => 'plugin_whatsapp',
					'type'    => 'text',
				) );




}


if ( ! function_exists( 'codplugin_custom_styles' ) ) :
/**
 * Custom style declarations
 * and custom user defined CSS in the document <head>
 *
 * Outputs CSS declarations generated by theme options
 *
 * @since codplugin 1.0
 */
function codplugin_custom_styles() { ?>

	<!--Customizer CSS--> 
	<style>


		#codplugin-checkout {border-color: <?php echo esc_html( get_theme_mod( 'accent_color', '#259bea') ); ?>; }

		.form-qte, #codplugin_add_button, #codplugin_count_button, #codplugin_remove_button, #codplugin_woo_single_form input, #codplugin_state, #codplugin_city, div#codplugin_order_history, #codplugin-checkout td, #codplugin-checkout .chosen-single, #codplugin-checkout .chosen-container .chosen-drop, .radio-variation-prices tr, .radio-variation-prices input[type="radio"]:before  {border-color: <?php echo esc_html( get_theme_mod( 'secondary_color', '#bce0f7') ); ?> !important; }

		span#codplugin_h_o i, div#codplugin_h_left i { color: <?php echo esc_html( get_theme_mod( 'secondary_color', '#bce0f7') ); ?>; }

		 div#codplugin_order_history,
		 #codplugin_show_hide, .radio-variation-prices tbody { background-color: <?php echo esc_html( get_theme_mod( 'order_summary_background', '#f0f9ff') ); ?>; }

		#codplugin-checkout, #codplugin_woo_single_form input, #codplugin_state, #codplugin_city {background-color: <?php echo esc_html( get_theme_mod( 'form_background_color', '#ffffff') ); ?>; }

		#codplugin-checkout,#codplugin_add_button, #codplugin_count_button, #codplugin_remove_button, #codplugin_woo_single_form input::placeholder, #codplugin_state, #codplugin_city, #codplugin-checkout .chosen-single {color: <?php echo esc_html( get_theme_mod( 'text_color', '#404040') ); ?>; }

		input#nrwooconfirm, .sticky-atc-btn a, #codplugin_count_number, #codplugin_d_free span, #nrwooconfirm.atc-buy-button .button,
#nrwooconfirm.atc-buy-button input, .radio-variation-prices input[type="radio"]:checked::before {background-color: <?php echo esc_html( get_theme_mod( 'button_color', '#259bea') ) ; ?> !important; }

		#codplugin-checkout .variation-prices bdi, #codplugin-checkout .full-price td:last-child {
			color: <?php echo esc_html( get_theme_mod( 'button_color', '#259bea') ) ; ?> !important; }

			.radio-variation-prices input[type="radio"]:checked::before,
			.radio-variation-prices tr.checked-var {
				border-color: <?php echo esc_html( get_theme_mod( 'button_color', '#259bea') ) ; ?> !important;
			}
			.checked::after {
				background-color: <?php echo esc_html( get_theme_mod( 'button_color', '#259bea') ) ; ?> !important;
			}


	</style>
	<!-- // Customizer CSS--> 
<?php }
endif;

add_action( 'wp_head', 'codplugin_custom_styles' );

