var napoleon_repeating_sortable_init=function(e){void 0===e?jQuery(".at-repeating-fields .inner").sortable({placeholder:"ui-state-highlight"}):jQuery(".at-repeating-fields .inner",e).sortable({placeholder:"ui-state-highlight"})},napoleon_repeating_colorpicker_init=function(e){if(void 0===e){jQuery("#widgets-right .napoleon-color-picker, #wp_inactive_widgets .napoleon-color-picker").filter(function(){return!jQuery(this).parents(".field-prototype").length}).wpColorPicker()}else jQuery(".napoleon-color-picker",e).wpColorPicker()};jQuery(document).ready(function(e){"use strict";var i=e("body");napoleon_repeating_sortable_init(),i.on("click",".at-repeating-add-field",function(i){var t=e(this).siblings(".inner");t.children(".field-prototype").clone(!0).removeClass("field-prototype").removeAttr("style").appendTo(t);napoleon_repeating_sortable_init(),napoleon_repeating_colorpicker_init(),i.preventDefault()}),i.on("click",".at-repeating-remove-field",function(i){e(this).parents(".post-field").trigger("change").remove(),i.preventDefault()})});