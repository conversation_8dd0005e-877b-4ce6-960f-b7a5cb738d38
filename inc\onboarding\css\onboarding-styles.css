.napoleon-onboarding-wrap .wp-badge {
  padding: 0;
  background: #fff;
  height: auto;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box {
  background: #fff;
  border-left: solid 4px #fff;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box .box-title {
  margin: 0 0 5px 0;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box .box-description {
  font-size: 13px;
  margin: 0;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box :last-child {
  margin-bottom: 0;
  margin-top: auto;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box.napoleon-variation .activate-variation {
  display: inline-block;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box.napoleon-variation.enabled {
  border-left: solid 4px green;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box.napoleon-variation.enabled .activate-variation {
  display: none;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box-warning {
  border-left: solid 4px red;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box-info {
  border-left: solid 4px orange;
}

.napoleon-onboarding-wrap .napoleon-onboarding-box-success {
  border-left: solid 4px green;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table {
  width: 100%;
  border-collapse: collapse;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table tr {
  border-bottom: solid 1px #ccc;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table tr:nth-child(even) {
  background: #f9f9f9;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table tr:nth-child(even) .napoleon-onboarding-pro {
  background: #9dba6f;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table th {
  font-weight: bold;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table th,
.napoleon-onboarding-wrap .napoleon-onboarding-table td {
  padding: 15px 8px;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table .napoleon-onboarding-col {
  width: 150px;
  text-align: center;
}

.napoleon-onboarding-wrap .napoleon-onboarding-table .napoleon-onboarding-pro {
  background: #92af64;
  color: #fff;
}

.napoleon-onboarding-wrap a.button-action {
  margin: 0 auto;
}

.napoleon-onboarding-wrap .disabled {
  pointer-events: none;
  cursor: not-allowed;
}

.napoleon-onboarding-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: 15px -15px 0 -15px;
}

.napoleon-onboarding-list .col {
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 20px;
  width: 100%;
}

@media (min-width: 1024px) {
  .napoleon-onboarding-wrap .napoleon-onboarding-box p {
    margin-top: 0;
  }

  .napoleon-onboarding-list .col {
    width: calc(33.33333% - 30px);
    margin-bottom: 50px;
  }

  .napoleon-onboarding-list .col > div {
    height: 100%;
  }
}