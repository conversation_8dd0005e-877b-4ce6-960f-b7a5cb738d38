!function(e){function t(t,o){var i;style='<style class="'+t+'">',style+=o.reduce(function(e,t){return e+=t.selectors+"{"+t.property+":"+t.value+";} "},""),style+="</style>",i=e("."+t),i.length?i.replaceWith(style):e("head").append(style)}wp.customize("blogname",function(t){t.bind(function(t){e(".site-logo a").text(t)})}),wp.customize("blogdescription",function(t){t.bind(function(t){e(".site-tagline").text(t)})}),wp.customize("hero_text_color",function(t){t.bind(function(t){e(".page-hero").css("color",t)})}),wp.customize("hero_image",function(t){t.bind(function(t){e(".page-hero").css("background-image","url("+t+")")})}),wp.customize("hero_bg_color",function(t){t.bind(function(t){e(".page-hero").css("background-color",t)})}),wp.customize("hero_image_repeat",function(t){t.bind(function(t){e(".page-hero").css("background-repeat",t)})}),wp.customize("hero_image_position_x",function(t){t.bind(function(t){var o=e(".page-hero"),i=o.css("background-position"),r=i.split(" ").map(function(e,o){return 0===o?t:e}).join(" ");o.css("background-position",r)})}),wp.customize("hero_image_position_y",function(t){t.bind(function(t){var o=e(".page-hero"),i=o.css("background-position"),r=i.split(" ").map(function(e,o){return 1===o?t:e}).join(" ");o.css("background-position",r)})}),wp.customize("hero_image_attachment",function(t){t.bind(function(t){e(".page-hero").css("background-attachment",t)})}),wp.customize("hero_image_cover",function(t){t.bind(function(t){t?e(".page-hero").css("background-size","cover"):e(".page-hero").css("background-size","auto")})}),wp.customize("header_primary_menu_padding",function(t){t.bind(function(t){e(".head-mast").css({paddingTop:t+"px",paddingBottom:t+"px"})})}),wp.customize("header_primary_menu_text_size",function(t){t.bind(function(t){e(".navigation-main > li > a").css(".navigation-main > li > a",t+"px")})}),wp.customize("header_background_color",function(t){t.bind(function(t){e(".head-mast").css("background-color",t)})}),wp.customize("header_primary_menu_bg_color",function(t){t.bind(function(t){e(".head-nav").css("background-color",t)})}),wp.customize("header_primary_menu_text_color",function(t){t.bind(function(t){e(".site-logo a, .site-tagline,.navigation-main > li > a,.header-mini-cart-trigger,.header-mini-cart-trigger .fas").css("color",t),e(".navigation-main .nav-button > a").css("border-color",t)})}),wp.customize("header_primary_menu_active_color",function(t){t.bind(function(t){e(".navigation-main > .current-menu-item > a,.navigation-main > .current-menu-parent > a,.navigation-main > .current-menu-ancestor > a").css("color",t)})}),wp.customize("header_primary_submenu_bg_color",function(t){t.bind(function(t){e(".navigation-main ul").css("background-color",t)})}),wp.customize("header_primary_submenu_text_color",function(t){t.bind(function(t){e(".navigation-main li li a").css("color",t)})}),wp.customize("header_primary_submenu_active_text_color",function(e){e.bind(function(e){t("header_primary_submenu_active_text_color",[{property:"color",value:e,selectors:".navigation-main li li:hover > a,.navigation-main li li > a:focus,.navigation-main li .current-menu-item > a,.navigation-main li .current-menu-parent > a,.navigation-main li .current-menu-ancestor > a"}])})}),wp.customize("theme_header_primary_menu_sticky",function(t){wp.customize.selectiveRefresh.bind("partial-content-rendered",function(t){e(".head-sticky").stick_in_parent({parent:"body",sticky_class:"is-stuck"})})}),wp.customize("footer_bg_color",function(t){t.bind(function(t){e(".footer-widgets").css("background-color",t)})}),wp.customize("footer_text_color",function(t){t.bind(function(t){e(".footer-widgets,.footer-widgets .widget,.footer-widgets .widget-title,.footer h1,.footer h2,.footer h3,.footer h4,.footer h5,.footer h6,.footer-widgets .at-contact-widget-item i").css("color",t)})}),wp.customize("footer_link_color",function(t){t.bind(function(t){e(".footer-widgets a,.footer-widgets .widget a").css("color",t)})}),wp.customize("footer_border_color",function(t){t.bind(function(t){e(".footer-widgets .social-icon,.footer-widgets .at-schedule-widget-table tr,.footer-widgets .widget_meta li a,.footer-widgets .widget_pages li a,.footer-widgets .widget_categories li a,.footer-widgets .widget_archive li a,.footer-widgets .widget_nav_menu li a,.footer-widgets .widget_product_categories li a,.footer-widgets .widget_layered_nav li a,.footer-widgets .widget_rating_filter li a,.footer-widgets .widget_recent_entries li,.footer-widgets .widget_recent_comments li,.footer-widgets .widget_rss li,.footer-widgets .tag-cloud-link").css("border-color",t)})}),wp.customize("footer_bottom_bg_color",function(t){t.bind(function(t){e(".footer-info").css("background-color",t)})}),wp.customize("footer_bottom_text_color",function(t){t.bind(function(t){e(".footer-info, .footer-info-addons .social-icon").css("color",t)})}),wp.customize("footer_bottom_link_color",function(t){t.bind(function(t){e(".footer-info a").css("color",t)})}),wp.customize("footer_titles_color",function(t){t.bind(function(t){e(".footer .widget-title, .footer h1,.footer h2, .footer h3, .footer h4, .footer h5, .footer h6").css("color",t)})}),wp.customize("sidebar_bg_color",function(t){t.bind(function(t){e(".sidebar").css({backgroundColor:t,padding:"20px"})})}),wp.customize("sidebar_text_color",function(t){t.bind(function(t){e(".sidebar,.sidebar .widget,.sidebar .at-contact-widget-item i").css("color",t)})}),wp.customize("sidebar_link_color",function(t){t.bind(function(t){e(".sidebar a, .sidebar .widget a").css("color",t)})}),wp.customize("sidebar_border_color",function(t){t.bind(function(t){e(".sidebar select, .sidebar input, .sidebar textarea").css("border-color",t),e(".sidebar .widget_recent_comments li,.sidebar .widget_recent_entries li,.sidebar .widget_rss li,.sidebar .widget_meta li a,.sidebar .widget_pages li a,.sidebar .widget_categories li a,.sidebar .widget_archive li a,.sidebar .widget_nav_menu li a").css("border-bottom-color",t)})}),wp.customize("sidebar_titles_color",function(t){t.bind(function(t){e(".sidebar .widget-title").css("color",t)})}),wp.customize("site_button_bg_color",function(t){t.bind(function(t){e('.btn,.button,.comment-reply-link,input[type="submit"],input[type="reset"],.wc-block-grid__products .add_to_cart_button,.wc-block-grid__products .added_to_cart,button[type="submit"]').css("background-color",t)})}),wp.customize("site_button_text_color",function(t){t.bind(function(t){e('.btn,.button,.comment-reply-link,input[type="submit"],input[type="reset"],.wc-block-grid__products .add_to_cart_button,.wc-block-grid__products .added_to_cart,button[type="submit"]').css("color",t)})}),wp.customize("site_button_hover_bg_color",function(t){t.bind(function(t){var o='<style class="site_button_hover_bg_color">.btn:hover,.button:hover,.comment-reply-link:hover,input[type="submit"]:hover,input[type="reset"]:hover,.wc-block-grid__products .add_to_cart_button:hover,.wc-block-grid__products .added_to_cart:hover,button[type="submit"]:hover{ background-color: '+t+" !important; }</style>",i=e(".site_button_hover_bg_color");i.length?i.replaceWith(o):e("head").append(o)})}),wp.customize("site_button_hover_text_color",function(t){t.bind(function(t){var o='<style class="site_button_hover_text_color">.btn:hover,.button:hover,.comment-reply-link:hover,input[type="submit"]:hover,input[type="reset"]:hover,.wc-block-grid__products .add_to_cart_button:hover,.wc-block-grid__products .added_to_cart:hover,button[type="submit"]:hover{ color: '+t+" !important; }</style>",i=e(".site_button_hover_bg_color");i.length?i.replaceWith(o):e("head").append(o)})}),wp.customize("content_h1_size",function(t){t.bind(function(t){e(".entry-content h1, .entry-title").css("font-size",t+"px")})}),wp.customize("content_h2_size",function(t){t.bind(function(t){e(".entry-content h2").css("font-size",t+"px")})}),wp.customize("content_h3_size",function(t){t.bind(function(t){e(".entry-content h3").css("font-size",t+"px")})}),wp.customize("content_h4_size",function(t){t.bind(function(t){e(".entry-content h4").css("font-size",t+"px")})}),wp.customize("content_h5_size",function(t){t.bind(function(t){e(".entry-content h5").css("font-size",t+"px")})}),wp.customize("content_h6_size",function(t){t.bind(function(t){e(".entry-content h6").css("font-size",t+"px")})}),wp.customize("content_body_size",function(t){t.bind(function(t){e(".entry-content").css("font-size",t+"px")})}),wp.customize("theme_widget_text_size",function(t){t.bind(function(t){e(".sidebar .widget,.footer .widget,.widget_meta li,.widget_pages li,.widget_categories li,.widget_archive li,.widget_nav_menu li,.widget_recent_entries li").css("font-size",t+"px")})}),wp.customize("theme_widget_title_size",function(t){t.bind(function(t){e(".widget-title").css("font-size",t+"px")})}),wp.customize("theme_lightbox",function(t){t.bind(function(t){t?e(".napoleon-lightbox, a[data-lightbox^='gal']").magnificPopup({type:"image",mainClass:"mfp-with-zoom",gallery:{enabled:!0},zoom:{enabled:!0}}):e(".napoleon-lightbox, a[data-lightbox^='gal']").off("click")})}),wp.customize("site_secondary_accent_color",function(e){e.bind(function(e){t("site_secondary_accent_color",[{property:"color",value:e,selectors:"a,a:hover,.site-tagline,.section-title > a,.entry-author-socials .social-icon,.widget-newsletter-content"}])})}),wp.customize("site_accent_color",function(e){e.bind(function(e){t("site_accent_color",[{property:"color",value:e,selectors:".entry-title a:hover,.item-title a:hover,.woocommerce-pagination a:hover,.woocommerce-pagination .current,.navigation a:hover,.navigation .current,.page-links .page-number:hover,.text-theme,.sidebar .social-icon:hover,.widget-newsletter-content-wrap .fas,.widget-newsletter-content-wrap .far,.widget_meta li a:hover,.widget_pages li a:hover,.widget_categories li a:hover,.widget_archive li a:hover,.widget_nav_menu li a:hover,.widget_product_categories li a:hover,.widget_layered_nav li a:hover,.widget_rating_filter li a:hover,.widget_recent_entries a:hover,.widget_recent_comments a:hover,.widget_rss a:hover,.shop-actions .product-number a.product-number-active,.shop-filter-toggle i,.star-rating,.comment-form-rating a,.woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a:hover,.product_list_widget .product-title:hover,.wc-block-grid__products .wc-block-grid__product-title:hover"},{property:"border-color",value:e,selectors:".sidebar .social-icon:hover"},{property:"background-color",value:e,selectors:".onsale,.wc-block-grid__products .wc-block-grid__product-onsale"}])})}),wp.customize("site_text_color",function(e){e.bind(function(e){t("site_text_color",[{property:"color",value:e,selectors:"body,blockquote cite,.instagram-pics li a,.category-search-select,.section-subtitle a,.entry-title a,.woocommerce-ordering select,.shop_table .product-name a,.woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a,.woocommerce-MyAccount-content mark,.woocommerce-MyAccount-downloads .download-file a,.woocommerce-Address-title a,.sidebar .widget_layered_nav_filters a,.row-slider-nav .slick-arrow,.comment-metadata a,.entry-meta,.item-meta,.item-meta a,.sidebar .widget_recent_entries .post-date,.sidebar .tag-cloud-link,.breadcrumb,.woocommerce-breadcrumb,.woocommerce-product-rating .woocommerce-review-link,.wc-tabs a,.sidebar .product_list_widget .quantity,.woocommerce-mini-cart__total"}])})}),wp.customize("site_text_color_secondary",function(e){e.bind(function(e){t("site_text_color_secondary",[{property:"color",value:e,selectors:".entry-meta a,.entry-tags a,.item-title a,.woocommerce-pagination a,.woocommerce-pagination span,.navigation a,.navigation .page-numbers,.page-links .page-number,.page-links > .page-number,.sidebar .social-icon,.sidebar-dismiss,.sidebar-dismiss:hover,.sidebar .widget_meta li a,.sidebar .widget_pages li a,.sidebar .widget_categories li a,.sidebar .widget_archive li a,.sidebar .widget_nav_menu li a,.sidebar .widget_product_categories li a,.sidebar .widget_layered_nav li a,.sidebar .widget_rating_filter li a,.sidebar .widget_recent_entries a,.sidebar .widget_recent_comments a,.sidebar .widget_rss a,.woocommerce-message a:not(.button),.woocommerce-error a:not(.button),.woocommerce-info a:not(.button),.woocommerce-noreview a:not(.button),.breadcrumb a,.woocommerce-breadcrumb a,.shop-actions a,.shop-filter-toggle,.entry-summary .product_title,.product_meta a,.entry-product-info .price,.tagged_as a,.woocommerce-grouped-product-list-item__label a,.reset_variations,.wc-tabs li.active a,.shop_table .remove,.shop_table .product-name a:hover,.shop_table .product-subtotal .woocommerce-Price-amount,.shipping-calculator-button,.sidebar .product_list_widget .product-title,.wc-block-grid__products .wc-block-grid__product-title"},{property:"background-color",value:e,selectors:".price_slider .ui-slider-handle"}])})}),wp.customize("site_text_color_supplementary",function(e){e.bind(function(e){t("site_text_color_supplementary",[{property:"color",value:e,selectors:".item .price,.item-inset,.woocommerce-grouped-product-list-item__price .woocommerce-Price-amount,.woocommerce-grouped-product-list-item__price del,.sidebar .product_list_widget .woocommerce-Price-amount,.sidebar .product_list_widget del,.wc-block-grid__products .wc-block-grid__product-price,.woocommerce-mini-cart__total .woocommerce-Price-amount"}])})}),wp.customize("site_border_color",function(e){e.bind(function(e){t("site_border_color",[{property:"border-color",value:e,selectors:"hr,blockquote,.entry-content th,.entry-content td,textarea,select,input,.no-comments,.header-mini-cart-contents,.entry-thumb img,.item,.item-media .item-thumb img,.sidebar .social-icon,.sidebar .at-schedule-widget-table tr,.sidebar .widget_meta li a,.sidebar .widget_pages li a,.sidebar .widget_categories li a,.sidebar .widget_archive li a,.sidebar .widget_nav_menu li a,.sidebar .widget_product_categories li a,.sidebar .widget_layered_nav li a,.sidebar .widget_rating_filter li a,.sidebar .widget_recent_entries li,.sidebar .widget_recent_comments li,.sidebar .widget_rss li,.demo_store,.woocommerce-product-gallery .flex-viewport,.woocommerce-product-gallery .flex-contorl-thumbs li img,.woocommerce-product-gallery__wrapper,.single-product-table-wrapper,.wc-tabs,.shop_table.cart,.shop_table.cart th,.shop_table.cart td,.cart-collaterals .shop_table,.cart-collaterals .shop_table th,.cart-collaterals .shop_table td,#order_review_heading,.wc_payment_method,.payment_box,.woocommerce-order-received .customer_details,.woocommerce-thankyou-order-details,.wc-bacs-bank-details,.woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a,.woocommerce-EditAccountForm fieldset,.wc-form-login,.sidebar .product_list_widget .product-thumb img,.header .widget_shopping_cart li.empty,.woocommerce-mini-cart__empty-message,.row-slider-nav .slick-arrow,textarea,select,input,.select2-container .select2-selection--single,.select2-container .select2-search--dropdown .select2-search__field,.select2-dropdown,wp-block-pullquote"},{property:"background-color",value:e,selectors:".price_slider.price_slider .ui-slider-range"}])})})}(jQuery);