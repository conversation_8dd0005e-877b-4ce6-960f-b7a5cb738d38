<?php
/**
 * Handles the Google Sheets Integration settings fields rendering and file handling logic.
 */
if (!class_exists('RID_COD_Google_Sheets_Settings')) { // Add class_exists check to prevent redeclaration

    class RID_COD_Google_Sheets_Settings {

        // Note: $option_group is not strictly needed here anymore as it's managed by the caller (Customizer)
        // private $option_group = 'rid_cod_google_sheets_options'; // Keep track of the option name

        /**
         * Constructor - No actions needed here anymore for page creation
         */
        public function __construct() {
            // Actions will be handled by the main settings class (RID_COD_Customizer)
        }

        /**
         * Render the description for the Google Sheets section.
         * Called by add_settings_section in RID_COD_Customizer.
         */
        public function render_section_description() {
            echo '<p>' . esc_html__('إعداد الإعدادات المطلوبة للاتصال وإرسال البيانات إلى جوجل شيت باستخدام Google Apps Script.', 'rid-cod') . '</p>';
            
            // YouTube Video Embed
            echo '<div style="margin: 20px 0;">';
            echo '<h4>' . esc_html__('شرح طريقة الإعداد:', 'rid-cod') . '</h4>';
            echo '<div style="position: relative; padding-bottom: 28%; height: 0; overflow: hidden; max-width: 500px; background: #000; margin: 0 auto;">';
            echo '<iframe src="https://www.youtube.com/embed/nOMbOG5Fp-c?si=YLxGpbAEZJu689kL" ';
            echo 'style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" ';
            echo 'frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" ';
            echo 'allowfullscreen></iframe>';
            echo '</div>';
            echo '</div>';
            
            // Useful Links
            echo '<div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 15px; margin: 20px 0; border-radius: 5px;">';
            echo '<h4 style="margin-top: 0;">' . esc_html__('روابط مفيدة:', 'rid-cod') . '</h4>';
            echo '<p style="margin: 10px 0;">';
            echo '<strong>🔗 ' . esc_html__('رابط جوجل شيتس:', 'rid-cod') . '</strong> ';
            echo '<a href="https://sheets.google.com" target="_blank" rel="noopener">https://sheets.google.com</a>';
            echo '</p>';
            echo '<p style="margin: 10px 0;">';
            echo '<strong>⚙️ ' . esc_html__('رابط جوجل سكريبت:', 'rid-cod') . '</strong> ';
            echo '<a href="https://script.google.com" target="_blank" rel="noopener">https://script.google.com</a>';
            echo '</p>';
            echo '<p style="margin: 10px 0; font-size: 12px; color: #666;">';
            echo '<em>' . esc_html__('💡 نصيحة: شاهد الفيديو أعلاه لمعرفة خطوات الإعداد التفصيلية.', 'rid-cod') . '</em>';
            echo '</p>';
            echo '</div>';

            // Apps Script Code Block - Directly echo escaped code within pre tag

            echo '<h4>' . esc_html__('كود Google Apps Script:', 'rid-cod') . '</h4>';
            echo '<p>' . esc_html__('انسخ الكود أدناه والصقه في محرر Apps Script.', 'rid-cod') . '</p>';
            // Add a wrapper div for positioning the button
            echo '<div style="position: relative;">';
            // Add ID to pre tag for JS targeting
            echo '<pre id="rid-cod-apps-script-code" style="white-space: pre-wrap; word-wrap: break-word; background-color: #f7f7f7; border: 1px solid #ccc; padding: 10px; font-family: monospace; max-height: 400px; overflow-y: auto; direction: ltr; text-align: left;">' .
                 esc_html('// --- CONFIGURATION ---
const SHEET_ID = "YOUR_SHEET_ID_HERE"; // <-- REPLACE with your actual Sheet ID (from the Sheet URL)
const SHEET_NAME = "Sheet1";           // <-- REPLACE with your target sheet name if different
// --- END CONFIGURATION ---

// Define the expected headers IN ARABIC (MUST match the order in your sheet and the data sent)
const HEADERS_AR = [
  "معرف المنتج", "اسم المنتج", "الكمية", "اللون", "القياس", "العرض", // Added Quantity, Color, Size, Width
  "اسم العميل", "الهاتف", "الولاية", "البلدية", "نوع التوصيل",
  "سعر المنتج", "تكلفة الشحن", "السعر الإجمالي", "تاريخ الطلب"
];
// Define corresponding keys expected from the POST data (MUST match HEADERS_AR order and data sent from PHP)
const DATA_KEYS = [
  \'product_id\', \'product_name\', \'quantity\', \'color\', \'size\', \'width\', // Added quantity, color, size, width
  \'customer_name\', \'phone\', \'state\', \'city\', \'delivery_type\',
  \'product_price\', \'shipping_cost\', \'total_price\', \'order_date\'
];


function addHeaderRowIfNeeded_(sheet) {
  try {
    // Check if sheet is completely empty
    if (sheet.getLastRow() === 0) {
      sheet.appendRow(HEADERS_AR);
      SpreadsheetApp.flush();
      Logger.log("✅ Header row added to empty sheet.");
      return;
    }

    // Check if the first row matches the Arabic headers
    const firstRow = sheet.getRange(1, 1, 1, HEADERS_AR.length).getValues()[0];
    if (JSON.stringify(firstRow) !== JSON.stringify(HEADERS_AR)) {
      // If first row doesn\'t match, insert headers at the top
      sheet.insertRowBefore(1);
      sheet.getRange(1, 1, 1, HEADERS_AR.length).setValues([HEADERS_AR]);
      SpreadsheetApp.flush();
      Logger.log("✅ Header row inserted/corrected.");
    }
  } catch (err) {
     Logger.log(`❌ Error in addHeaderRowIfNeeded_: ${err.message}`);
  }
}


function doPost(e) {
  try {
    const ss = SpreadsheetApp.openById(SHEET_ID);
    let sheet = ss.getSheetByName(SHEET_NAME);

    if (!sheet) {
      Logger.log(`⚠️ Sheet "${SHEET_NAME}" not found. Using active sheet instead.`);
      sheet = ss.getActiveSheet();
    }

    // --- Add Header Row if Needed ---
    addHeaderRowIfNeeded_(sheet);
    // --------------------------------

    const postData = JSON.parse(e.postData.contents);

    // Prepare the row data based on DATA_KEYS order
    const newRow = DATA_KEYS.map(key => {
      if (key === \'order_date\') {
        return new Date(); // Add current timestamp
      }
      // Use the value from postData if it exists, otherwise use an empty string
      return postData.hasOwnProperty(key) ? postData[key] : \'\';
    });

    // Append the new row
    sheet.appendRow(newRow);

    // Return success response
    return ContentService.createTextOutput(JSON.stringify({ status: "success", message: "Data received ✅" }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    Logger.log(`❌ Error in doPost: ${error.message}\\nStack: ${error.stack}`);
    Logger.log(`Received Data: ${e.postData ? e.postData.contents : \'No data\'}`);
    // Return error response
    return ContentService.createTextOutput(JSON.stringify({ status: "error", message: error.message }))
      .setMimeType(ContentService.MimeType.JSON)
      .setStatusCode(500); // Use 500 for server errors
  }
}

// Optional: Simple doGet for testing the deployment URL in a browser
function doGet(e) {
  return ContentService.createTextOutput("🟢 RIDCODE Apps Script is running. Use POST to send data.");
}') . // End of script string and esc_html()
                 '</pre>';
            // Add the copy button
            echo '<button type="button" id="rid-cod-copy-script-button" class="button" style="position: absolute; top: 5px; right: 5px;">' . esc_html__('Copy Code', 'rid-cod') . '</button>';
            echo '</div>'; // Close wrapper div

        }

        // --- Field Rendering Callbacks (Public to be called from Customizer) ---

        /**
         * Renders a text input field for Google Sheets settings.
         * Expects 'id', 'option_group', 'desc', 'placeholder' in $args.
         */
        public function render_google_sheets_text_field($args) {
            $option_group_name = $args['option_group'] ?? 'rid_cod_settings_options'; // Get option group from args
            $options = get_option($option_group_name);
            $value = isset($options[$args['id']]) ? $options[$args['id']] : '';
            $placeholder = $args['placeholder'] ?? '';
            ?>
            <input type="text" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo esc_attr($option_group_name . '[' . $args['id'] . ']'); ?>" value="<?php echo esc_attr($value); ?>" class="regular-text" placeholder="<?php echo esc_attr($placeholder); ?>" />
            <?php if (isset($args['desc'])) : ?>
                <p class="description"><?php echo esc_html($args['desc']); ?></p>
            <?php endif; ?>
            <?php
        }

        /**
         * Renders a checkbox field for Google Sheets settings.
         * Expects 'id', 'option_group', 'desc' in $args.
         */
         public function render_google_sheets_checkbox_field($args) {
            $option_group_name = $args['option_group'] ?? 'rid_cod_settings_options';
            $options = get_option($option_group_name);
            $checked = isset($options[$args['id']]) ? $options[$args['id']] : 'no'; // Default to 'no'
            ?>
            <input type="checkbox" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo esc_attr($option_group_name . '[' . $args['id'] . ']'); ?>" value="yes" <?php checked($checked, 'yes'); ?> />
            <?php if (isset($args['desc'])) : ?>
                 <label for="<?php echo esc_attr($args['id']); ?>"><p class="description" style="display: inline-block; margin-left: 5px;"><?php echo esc_html($args['desc']); ?></p></label>
            <?php endif; ?>
            <?php
        }

        // Removed render_google_sheets_file_field() and handle_google_sheets_file_upload()
        // as they are no longer needed for the Google Apps Script method.

    } // End class RID_COD_Google_Sheets_Settings

} // End if (!class_exists('RID_COD_Google_Sheets_Settings'))