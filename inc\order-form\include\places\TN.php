<?php

/**
 * Districts of Tunisian
 * 
 * Source:
 * - https://en.wikipedia.org/wiki/Subdivisions_of_Tunisia
 * 
 * Updated: March 22, 2020
 * By <PERSON><PERSON><PERSON> <<EMAIL>> | https://yordansoar.es
 * <AUTHOR> <<EMAIL>>
 * @version  1.0.0
 * @license http://opensource.org/licenses/gpl-license.php GNU Public License
 * Credits: https://donjajo.com/ngstateslga-nigerian-36-states-local-government-areas-php-class/
 */
global $places;

$places['TN'] = array(
	'AR' => array(
		'<PERSON>na <PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON>rg<PERSON><PERSON>',
		'Cité El Ghazela',
		'Ennasr 1',
		'Ennasr 2',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON> Landlous',
		'La Soukra',
		'<PERSON>zah 1',
		'<PERSON>zah 5',
		'<PERSON>zah 6',
		'<PERSON><PERSON>h 7',
		'<PERSON>zah 8',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON>',
		'R<PERSON>dh El Andalous',
		'<PERSON><PERSON>'
	),
	'BA' => array(
		'<PERSON>',
		'<PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON>j <PERSON><PERSON><PERSON>',
		'<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON> <PERSON><PERSON><PERSON><PERSON>',
		'<PERSON> <PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON> <PERSON><PERSON>',
		'<PERSON><PERSON><PERSON> <PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON>',
		'<PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON> 1',
		'<PERSON><PERSON><PERSON><PERSON> 3',
		'<PERSON><PERSON><PERSON><PERSON> 4',
		'<PERSON>urouj 5',
		'Mourouj 6',
		'Naassen',
		'Nouvelle Medina',
		'Rades'
	),
	'BZ' => array(
		'Bizerte Nord',
		'Bizerte Sud',
		'El Alia',
		'Ghar El Melh',
		'Ghezala',
		'Joumine',
		'Mateur',
		'Menzel Bourguiba',
		'Menzel Jemil',
		'Ras Jebel',
		'Sejnane',
		'Tinja',
		'Utique',
		'Zarzouna'
	),
	'BE' => array(
		'Amdoun',
		'Beja Nord',
		'Beja Sud',
		'Goubellat',
		'Mejez El Bab',
		'Nefza',
		'Teboursouk',
		'Testour',
		'Thibar'
	),
	'GB' => array(
		'El Hamma',
		'El Metouia',
		'Gabes Medina',
		'Gabes Ouest',
		'Gabes Sud',
		'Ghannouche',
		'Mareth',
		'Matmata',
		'Menzel Habib',
		'Nouvelle Matmata'
	),
	'GF' => array(
		'Belkhir',
		'El Guettar',
		'El Ksar',
		'El Mdhilla',
		'Gafsa Nord',
		'Gafsa Sud',
		'Metlaoui',
		'Moulares',
		'Redeyef',
		'Sidi Aich',
		'Sned'
	),
	'JD' => array(
		'Ain Draham',
		'Balta Bou Aouene ',
		'Bou Salem',
		'Fernana',
		'Ghardimaou',
		'Jendouba',
		'Jendouba Nord',
		'Oued Mliz',
		'Tabarka'
	),
	'KR' => array(
		'Bou Hajla',
		'Chebika',
		'Cherarda',
		'El Ala',
		'Haffouz',
		'Hajeb El Ayoun',
		'Kairouan Nord',
		'Kairouan Sud',
		'Nasrallah',
		'Oueslatia',
		'Sbikha'
	),
	'KS' => array(
		'El Ayoun',
		'Ezzouhour',
		'Feriana',
		'Foussana',
		'Haidra',
		'Hassi El Frid',
		'Jediliane',
		'Kasserine Nord',
		'Kasserine Sud',
		'Mejel Bel Abbes',
		'Sbeitla',
		'Sbiba',
		'Thala'
	),
	'KB' => array(
		'Douz',
		'El Faouar',
		'Kebili Nord',
		'Kebili Sud',
		'Souk El Ahad'
	),
	'LM' => array(
		'Borj El Amri',
		'Borj Etoumi',
		'Chabbaou',
		'Chaouat',
		'Complexe Universitaire',
		'Denden',
		'Douar Hicher',
		'Eddkhila',
		'El Battan',
		'El Fejja',
		'Jedaida',
		'Mannouba',
		'Mornaguia',
		'Oued Ellil',
		'Tebourba'
	),
	'LK'  => array(
		'Dahmani',
		'El Ksour',
		'Jerissa',
		'Kalaa El Khasba',
		'Kalaat Sinane',
		'Le Kef Est',
		'Le Kef Ouest',
		'Le Sers',
		'Nebeur',
		'Sakiet Sidi Youssef',
		'Tajerouine',
		'Touiref'
	),
	'MH' => array(
		'Bou Merdes',
		'Chorbane',
		'El Jem',
		'Hbira',
		'Ksour Essaf',
		'La Chebba',
		'Mahdia',
		'Melloulech',
		'Ouled Chamakh',
		'Sidi Alouene',
		'Souassi'
	),
	'MN' => array(
		'Bekalta',
		'Bembla',
		'Beni Hassen',
		'Jemmal',
		'Ksar Helal',
		'Ksibet El Mediouni',
		'Moknine',
		'Monastir',
		'Ouerdanine',
		'Sahline',
		'Sayada Lamta Bou Hajar',
		'Teboulba',
		'Zeramdine'
	),
	'MD' => array(
		'Ajim',
		'Ben Guerdane',
		'Beni Khedache',
		'Houmet Essouk',
		'Medenine Nord',
		'Medenine Sud',
		'Midoun',
		'Sidi Makhlouf',
		'Zarzis'
	),
	'NB' => array(
		'Beni Khalled',
		'Beni Khiar',
		'Bou Argoub',
		'Dar Chaabane Elfehri',
		'El Haouaria',
		'El Mida',
		'Grombalia',
		'Hammam El Ghezaz',
		'Hammamet',
		'Kelibia',
		'Korba',
		'Menzel Bouzelfa',
		'Menzel Temime',
		'Nabeul',
		'Soliman',
		'Takelsa'
	),
	'SF' => array(
		'Agareb',
		'Bir Ali Ben Khelifa',
		'El Amra',
		'El Hencha',
		'Esskhira',
		'Ghraiba',
		'Jebeniana',
		'Kerkenah',
		'Mahras',
		'Menzel Chaker',
		'Sakiet Eddaier',
		'Sakiet Ezzit',
		'Sfax Est',
		'Sfax Sud',
		'Sfax Ville'
	),
	'SB' => array(
		'Ben Oun',
		'Bir El Haffey',
		'Cebbala',
		'Maknassy',
		'Menzel Bouzaiene',
		'Mezzouna',
		'Ouled Haffouz',
		'Regueb',
		'Sidi Bouzid Est',
		'Sidi Bouzid Ouest',
		'Souk Jedid'
	),
	'SI' => array(
		'Bargou',
		'Bou Arada',
		'El Aroussa',
		'Gaafour',
		'Kesra',
		'Le Krib',
		'Makthar',
		'Rohia',
		'Sidi Bou Rouis',
		'Siliana Nord',
		'Siliana Sud'
	),
	'SS' => array(
		'Akouda',
		'Bou Ficha',
		'Enfidha',
		'Hammam Sousse',
		'Hergla',
		'Kalaa El Kebira',
		'Kalaa Essghira',
		'Kondar',
		'Msaken',
		'Sidi Bou Ali',
		'Sidi El Heni',
		'Sousse Jaouhara',
		'Sousse Riadh',
		'Sousse Ville'
	),
	'TT' => array(
		'Bir Lahmar',
		'Dehiba',
		'Ghomrassen',
		'Remada',
		'Smar',
		'Tataouine Nord',
		'Tataouine Sud'
	),
	'TZ' => array(
		'Degueche',
		'Hezoua',
		'Nefta',
		'Tameghza',
		'Tozeur'
	),
	'TS' => array(
		'Bab Bhar',
		'Bab Bnet',
		'Bab Fella',
		'Bab Khadhra',
		'Bab Mnara',
		'Bab Saadoun',
		'Bab Souika',
		'Belvedère',
		'Carthage',
		'Cite El Khadra',
		'Cité El Mahrajen',
		'Cité Hlel',
		'Cité Ibn Khaldoun',
		'Cité Ibn Sina',
		'Cité Intilaka',
		'Cité Rommana',
		'El Agba',
		'El Aouina',
		'El Hrairia',
		'El Kabbaria',
		'El Kram',
		'El Menzah',
		'El Omrane',
		'El Omrane Superieur',
		'El Ouerdia',
		'Essijoumi',
		'Ettahrir',
		'Ezzouhour',
		'Gammart',
		'Hafsia',
		'Jardins De Carthage',
		'Jebel Jelloud',
		'La Goulette',
		'La Marsa',
		'La Medina',
		'Le Bardo',
		'Les Berges Du Lac 1',
		'Les Berges Du Lac 2',
		'Manar 1',
		'Manar 2',
		'Manar 3',
		'Menzah 4',
		'Menzah 9',
		'Monplaisir',
		'Mourouj 2',
		'Sidi Bou Saïd',
		'Sidi El Bechir',
		'Sidi Hassine'
	),
	'ZG' => array(
		'Bir Mcherga',
		'El Fahs',
		'Ennadhour',
		'Hammam Zriba',
		'Saouef',
		'Zaghouan'
	)
);

// Use this filter to handle the Districts of Tunisian 
$places['TN'] = apply_filters('scpwoo_custom_places_tn', $places['TN']);
