/**
 * Alpha Color Picker CSS
 */

.customize-control-alpha-color .wp-picker-container .iris-picker {
	border-bottom:none;
}

.customize-control-alpha-color .wp-picker-container {
	max-width: 257px;
}

.customize-control-alpha-color .wp-picker-open + .wp-picker-input-wrap {
	width: 100%;
	overflow: hidden;
}

.customize-control-alpha-color .wp-picker-input-wrap input[type="text"].wp-color-picker.alpha-color-control {
	float: left;
	width: 195px;
}

.customize-control-alpha-color .wp-picker-input-wrap .button {
	margin-left: 0;
	float: right;
}

.wp-picker-container .wp-picker-open ~ .wp-picker-holder .alpha-color-picker-container {
	display: block;
}

.alpha-color-picker-container {
	border: 1px solid #dfdfdf;
	border-top: none;
	display: none;
	background: #FFF;
	padding: 0 11px 10px;
	position: relative;
}

.alpha-color-picker-container .ui-widget-content,
.alpha-color-picker-container .ui-widget-header,
.alpha-color-picker-wrap .ui-state-focus {
	background: transparent;
	border: none;
}

.alpha-color-picker-wrap a.iris-square-value:focus {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.alpha-color-picker-container .ui-slider {
	position: relative;
	z-index: 1;
	height: 24px;
	text-align: center;
	margin: 0 auto;
	width: 88%;
	width: calc( 100% - 28px );
}

.alpha-color-picker-container .ui-slider-handle,
.alpha-color-picker-container .ui-widget-content .ui-state-default {
	color: #777;
	background-color: #FFF;
	text-shadow: 0 1px 0 #FFF;
	text-decoration: none;
	position: absolute;
	z-index: 2;
	box-shadow: 0 1px 2px rgba(0,0,0,0.2);
	border: 1px solid #aaa;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	margin-top: -2px;
	top: 0;
	height: 26px;
	width: 26px;
	cursor: ew-resize;
	font-size: 0;
	padding: 0;
	line-height: 27px;
	margin-left: -14px;
}

.alpha-color-picker-container .ui-slider-handle.show-opacity {
	font-size: 12px;
}

.alpha-color-picker-container .click-zone {
	width: 14px;
	height: 24px;
	display: block;
	position: absolute;
	left: 10px;
}

.alpha-color-picker-container .max-click-zone {
	right: 10px;
	left: auto;
}

.alpha-color-picker-container .transparency {
	height: 24px;
	width: 100%;
	background-color: #FFF;
	background-image: url(transparency-grid.png);
	box-shadow: 0 0 5px rgba(0,0,0,0.4) inset;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	padding: 0;
	margin-top: -24px;
}

@media only screen and (max-width: 782px) {
	.customize-control-alpha-color .wp-picker-input-wrap input[type="text"].wp-color-picker.alpha-color-control {
		width: 184px;
	}
}

@media only screen and (max-width: 640px) {
	.customize-control-alpha-color .wp-picker-input-wrap input[type="text"].wp-color-picker.alpha-color-control {
		width: 172px;
		height: 33px;
	}
}
