<?php
/**
 * Algeria States and Cities data
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Get Algeria states
 *
 * @return array
 */
function rid_cod_get_algeria_states() {
    return array(
        '01' => 'أدرار',
        '02' => 'الشلف',
        '03' => 'الأغواط',
        '04' => 'أم البواقي',
        '05' => 'باتنة',
        '06' => 'بجاية',
        '07' => 'بسكرة',
        '08' => 'بشار',
        '09' => 'البليدة',
        '10' => 'البويرة',
        '11' => 'تمنراست',
        '12' => 'تبسة',
        '13' => 'تلمسان',
        '14' => 'تيارت',
        '15' => 'تيزي وزو',
        '16' => 'الجزائر',
        '17' => 'الجلفة',
        '18' => 'جيجل',
        '19' => 'سطيف',
        '20' => 'سعيدة',
        '21' => 'سكيكدة',
        '22' => 'سيدي بلعباس',
        '23' => 'عنابة',
        '24' => 'قالمة',
        '25' => 'قسنطينة',
        '26' => 'المدية',
        '27' => 'مستغانم',
        '28' => 'المسيلة',
        '29' => 'معسكر',
        '30' => 'ورقلة',
        '31' => 'وهران',
        '32' => 'البيض',
        '33' => 'إليزي',
        '34' => 'برج بوعريريج',
        '35' => 'بومرداس',
        '36' => 'الطارف',
        '37' => 'تندوف',
        '38' => 'تيسمسيلت',
        '39' => 'الوادي',
        '40' => 'خنشلة',
        '41' => 'سوق أهراس',
        '42' => 'تيبازة',
        '43' => 'ميلة',
        '44' => 'عين الدفلى',
        '45' => 'النعامة',
        '46' => 'عين تموشنت',
        '47' => 'غرداية',
        '48' => 'غليزان',
        '49' => 'تيميمون',
        '50' => 'برج باجي مختار',
        '51' => 'أولاد جلال',
        '52' => 'بني عباس',
        '53' => 'عين صالح',
        '54' => 'عين قزام',
        '55' => 'توقرت',
        '56' => 'جانت',
        '57' => 'المغير',
        '58' => 'المنيعة'
    );
}

/**
 * Get cities by state code
 *
 * @param string $state_code
 * @return array
 */
function rid_cod_get_cities_by_state($state_code = '') {
    $cities = array(
'01' => array('Adrar', 'Adrar', 'Akabli', 'Aoulef', 'Bouda', 'Fenoughil', 'In Zghmir', 'Ksar Kaddour', 'Ouled Ahmed Tammi', 'Reggane', 'Sali', 'Sebaa', 'Tamantit', 'Tamekten', 'Tamest', 'Tit', 'Tsabit', 'Zaouiet Kounta'),
'02' => array('Chlef', 'Abou El Hassan', 'Aïn Merane', 'Bénairia', 'Beni Bouateb', 'Beni Haoua', 'Beni Rached', 'Boukadir', 'Breira', 'Chettia', 'Chlef', 'Dahra', 'El Hadjadj', 'El Karimia', 'El Marsa', 'Harchoun', 'Harenfa', 'Labiod Medjadja', 'Moussadek', 'Oued Fodda', 'Oued Goussine', 'Oued Sly', 'Ouled Abbes', 'Ouled Ben Abdelkader', 'Ouled Fares', 'Oum Drou', 'Sendjas', 'Sidi Abderrahmane', 'Sidi Akkacha', 'Sobha', 'Tadjena', 'Taougrite', 'Ténès', 'Zeboudja'),
'03' => array('Laghouat', 'Aflou', 'Aïn Madhi', 'Aïn Sidi Ali', 'Beidha', 'Brida', 'El Assafia', 'El Ghicha', 'El Houaita', 'Gueltat Sidi Saad', 'Hadj Mechri', 'Hassiane Diab', 'Kheneg', 'Ksar El Hirane', 'Laghouat', 'Oued Morra', 'Oued M\'Zi', 'Sebgag', 'Sidi Bouzid', 'Sidi Makhlouf', 'Tadjemout', 'Tadjrouna', 'Taouiala'),
'04' => array('Oum El Bouaghi', 'Aïn Babouche', 'Aïn Beida', 'Aïn Diss', 'Aïn Fakroun', 'Aïn Kercha', 'Aïn M\'lila', 'Aïn Zitoun', 'Behir Chergui', 'Berriche', 'Bir Chouhada', 'Dhalaa', 'El Amiria', 'El Belala', 'El Djazia', 'El Fedjoudj Boughrara Saoudi', 'El Harmilia', 'Fkirina', 'Hanchir Toumghani', 'Ksar Sbahi', 'Meskiana', 'O.E.Bouaghi', 'Ouled Gacem', 'Ouled Hamla', 'Ouled Zouai', 'Rahia', 'Sigus', 'Souk Naamane', 'Zorg'),
'05' => array('Batna', 'Aïn Djasser', 'Aïn Touta', 'Aïn Yagout', 'Arris', 'Azzil Abdelkader', 'Batna', 'Barika', 'Beni Foudhala El Hakania', 'Bitam', 'Boulhilat', 'Boumagueur', 'Boumia', 'Bouzina', 'Chemora', 'Chir', 'Djerma', 'Djezzar', 'El Hassi', 'El Madher', 'Fesdis', 'Foum Toub', 'Ghassira', 'Gosbat', 'Guigba', 'Hidoussa', 'Ichemoul', 'Inoughissen', 'Kimmel', 'Ksar Belezma', 'Larbaâ', 'Lazrou', 'Lemsane', 'M\'doukal', 'Maafa', 'Menaa', 'Merouana', 'N\'Gaous', 'Oued Chaaba', 'Oued El Ma', 'Oued Taga', 'Ouled Ammar', 'Ouled Aouf', 'Ouled Fadel', 'Ouled Sellam', 'Ouyoun El Assafir', 'Rahbat', 'Ras El Aioun', 'Sefiane', 'Seggana', 'Seriana', 'T\'Kout', 'Talkhamt', 'Tazoult', 'Teniet El Abed', 'Theniet El Abed', 'Tilatou', 'Tighanimine', 'Tigherghar', 'Timgad', 'Zanet El Beida'),
'06' => array('Béjaïa', 'Adekar', 'Aït Rizine', 'Aït Smail', 'Akbou', 'Amalou', 'Amizour', 'Aokas', 'Barbacha', 'Béjaïa', 'Beni Djellil', 'Beni Ksila', 'Beni Maouche', 'Boudjellil', 'Bouhamza', 'Boukhelifa', 'Chellata', 'Chemini', 'Darguina', 'Draâ El Kaïd', 'El Kseur', 'Fenaia Ilmaten', 'Feraoun', 'Ifri Ouzellaguen', 'Ighil Ali', 'Ighram', 'Kendira', 'Kherrata', 'Leflaye', 'M\'cisna', 'Melbou', 'Oued Ghir', 'Ouzellaguen', 'Seddouk', 'Semaoune', 'Sidi Aïch', 'Sidi Ayad', 'Souk El Ténine', 'Souk Oufella', 'Tala Hamza', 'Tamokra', 'Tamridjet', 'Taourit Ighil', 'Taskriout', 'Tazmalt', 'Tibane', 'Tichy', 'Tifra', 'Timezrit', 'Tinebdar', 'Tizi N\'Berber', 'Toudja'),
'07' => array('Biskra', 'Aïn Naga', 'Aïn Zaatout', 'Biskra', 'Bordj Ben Azzouz', 'Bouchagroune', 'Branis', 'Chetma', 'Djemorah', 'El Feidh', 'El Ghrous', 'El Haouch', 'El Kantara', 'El Mizaraa', 'El Outaya', 'Foughala', 'Khenguet Sidi Nadji', 'Lichana', 'Lioua', 'M\'Chouneche', 'Mekhadma', 'M\'Lili',  'Ouled Harkat', 'Ouled Rahma', 'Ouled Sassi', 'Oumache', 'Sidi Okba', 'Tolga', 'Zeribet El Oued'),
'08' => array('Béchar', 'Abadla', 'Béchar', 'Béni Ikhlef', 'Boukais', 'Erg Ferradj', 'Ighil', 'Kenadsa', 'Lahmar', 'Mechraa Houari Boumedienne', 'Meridja', 'Mogheul', 'Taghit', 'Timoudi'),
'09' => array('Blida', 'Aïn Romana', 'Béni Mered', 'Béni Tamou', 'Blida', 'Bouarfa', 'Boufarik', 'Bouinan', 'Chebli', 'Chiffa', 'Chréa', 'Daouda', 'El Affroun', 'Guerouaou', 'Larbaâ', 'Meftah', 'Mozaïa', 'Oued Djer', 'Oued El Alleug', 'Ouled Slama', 'Ouled Yaïch', 'Souhane', 'Soumaa', 'Sidi Moussa', 'Hammam Melouane'),
'10' => array('Bouira', 'Aghbalou', 'Aïn Bessem', 'Aïn El Hadjar', 'Aïn El Turc', 'Aïn Laloui', 'Ahl El Ksar', 'Aomar', 'Ath Mansour', 'Bechloul', 'Bir Ghbalou', 'Boukram', 'Bouira', 'Chorfa', 'Dechmia', 'Dirrah', 'Djebahia', 'El Adjiba', 'El Asnam', 'El Hachimia', 'El Hakimia', 'El Khabouzia', 'El Mokrani', 'Guerrouma', 'Haizer', 'Hanif', 'Kadiria', 'Lakhdaria', 'M\'Chedallah', 'Mezdour', 'Oued El Berdi', 'Ouled Rached', 'Raouraoua', 'Ridane', 'Saharidj', 'Sour El Ghouzlane', 'Souk El Khemis', 'Taguedit', 'Taghzout', 'Zbarbar'),
'11' => array('Tamanrasset', 'Abalessa', 'Idles', 'In Amguel', 'Tamanrasset', 'Tit'),
'12' => array('Tébessa', 'Aïn Zerga', 'Bedjene', 'Bekkaria', 'Bir Dheheb', 'Bir El Ater', 'Bir Mokadem', 'Boukhadra', 'Boulhaf Dir', 'Cheria', 'El Aouinet', 'El Kouif', 'El Ma Labiodh', 'El Meridj', 'El Ogla', 'El Ogla El Malha', 'Ferkane', 'Guorriguer', 'Hammamet', 'Lahouidjbet', 'Morsott', 'Negrine', 'Oum Ali', 'Ouenza', 'Safsaf El Ouesra', 'Stah Guentis', 'Tébessa', 'Tlidjene'),
'13' => array('Tlemcen', 'Aïn Fezza', 'Aïn Ghoraba', 'Aïn Nehala', 'Aïn Tallout', 'Aïn Youcef', 'Amieur', 'Azails', 'Bab El Assa', 'Beni Bahdel', 'Beni Boussaid', 'Beni Mester', 'Beni Ouarsous', 'Beni Semiel', 'Beni Snous', 'Bensekrane', 'Bouhlou', 'Chetouane', 'Dar Yaghmoracen', 'Djebala', 'El Aricha', 'El Bouihi', 'El Fehoul', 'El Gor', 'El Mefrouche', 'Fellaoucene', 'Ghazaouet', 'Hammam Boughrara', 'Hennaya', 'Honnaine', 'Maghnia', 'Mansourah', 'M\'Sirda Fouaga', 'Marsa Ben M\'Hidi', 'Msirda Fouaga', 'Nedroma', 'Oued Chouly', 'Oued Lakhdar', 'Ouled Mimoun', 'Ouled Riyah', 'Remchi', 'Sabra', 'Sebaa Chioukh', 'Sebdou', 'Sidi Abdelli', 'Sidi Djillali', 'Sidi Medjahed', 'Souk Tlata', 'Souahlia', 'Souani', 'Tianet', 'Tlemcen', 'Zenata'),
'14' => array('Tiaret', 'Aïn Bouchekif', 'Aïn Deheb', 'Aïn El Hadid', 'Aïn Kermes', 'Aïn Dzarit', 'Bougara', 'Chehaima', 'Dahmouni', 'Djebilet Rosfa', 'Djillali Ben Amar', 'Faidja', 'Frenda', 'Guertoufa', 'Hamadia', 'Ksar Chellala', 'Madna', 'Mahdia', 'Mechraa Safa', 'Medrissa', 'Medroussa', 'Meghila', 'Mellakou', 'Nadorah', 'Naima', 'Oued Lilli', 'Rahouia', 'Rechaiga', 'Sebaine', 'Sebt', 'Serghine', 'Si Abdelghani', 'Sidi Abderrahmane', 'Sidi Ali Mellal', 'Sidi Bakhti', 'Sidi Hosni', 'Sougueur', 'Tagdempt', 'Takhemaret', 'Tiaret', 'Tidda', 'Tousnina', 'Zmalet El Emir Abdelkader'),
'15' => array('Tizi Ouzou', 'Aït Aggouacha', 'Aït Bouaddou', 'Aït Boumahdi', 'Aït Chafâa', 'Aït Khellili', 'Aït Mahmoud', 'Aït Oumalou', 'Aït Toudert', 'Aït Yahia', 'Aït Yahia Moussa', 'Aït Zmenzer', 'Akbil', 'Akerrou', 'Assi Youcef', 'Azazga', 'Azeffoun', 'Beni Douala', 'Beni Ziki', 'Beni Zmenzer', 'Boghni', 'Boudjima', 'Bounouh', 'Bouzeguène', 'Draâ Ben Khedda', 'Draâ El Mizan', 'Freha', 'Frikat', 'Iboudrarène', 'Idjeur', 'Iferhounène', 'Ifigha', 'Iflissen', 'Illilten', 'Illoula Oumalou', 'Imkiren', 'Imsouhel', 'Irdjen', 'Larbaa Nath Irathen', 'Maâtkas', 'Makouda', 'Mekla', 'Mizrana', 'Moknéa', 'Ouacif', 'Ouadhia', 'Ouaguenoun', 'Sidi Namane', 'Souamaâ', 'Souk El Thenine', 'Tadmaït', 'Tigzirt', 'Timizart', 'Tirmitine', 'Tizi N\'Tleta', 'Tizi Ouzou', 'Tizi Rached', 'Yakouren', 'Yatafen', 'Zekri'),
'16' => array('Alger', 'Aïn Benian', 'Aïn Taya', 'Alger Centre', 'Bab El Oued', 'Bab Ezzouar', 'Bachdjerrah', 'Baraki', 'Béni Messous', 'Bir Mourad Raïs', 'Birtouta', 'Bologhine', 'Bordj El Bahri', 'Bordj El Kiffan', 'Bourouba', 'Bouzareah', 'Casbah', 'Chéraga', 'Dar El Beïda', 'Dely Ibrahim', 'Douera', 'Draria', 'El Achour', 'El Biar', 'El Harrach', 'El Madania', 'El Magharia', 'El Marsa', 'El Mouradia', 'Gué de Constantine', 'Hammamet', 'Herraoua', 'Hydra', 'Hussein Dey', 'Khraicia', 'Kouba', 'Les Eucalyptus', 'Mahelma', 'Mohammadia', 'Oued Koriche', 'Oued Smar', 'Ouled Chebel', 'Ouled Fayet', 'Rahmania', 'Raïs Hamidou', 'Réghaïa', 'Rouiba', 'Saoula', 'Sidi M\'Hamed', 'Sidi Moussa', 'Souidania', 'Staoueli', 'Tessala El Merdja', 'Zéralda'),
'17' => array('Djelfa', 'Aïn Chouhada', 'Aïn El Bell', 'Aïn El Ibel', 'Aïn Feka', 'Aïn Maabed', 'Aïn Oussera', 'Amourah', 'Benhar', 'Beni Yacoub', 'Birine', 'Bouira Lahdab', 'Charef', 'Dar Chioukh', 'Deldoul', 'Djelfa', 'Douis', 'El Guedid', 'El Idrissia', 'El Khemis', 'Faidh El Botma', 'Guernini', 'Guettara', 'Had Sahary', 'Hassi Bahbah', 'Hassi El Euch', 'Hassi Fedoul', 'Messaad', 'M\'Liliha', 'Moudjebara', 'Oum Laadham', 'Sed Rahal', 'Selmana', 'Sidi Baizid', 'Sidi Ladjel', 'Tadmit', 'Zaafrane', 'Zaccar'),
'18' => array('Jijel', 'Jijel', 'Erraguene', 'El Aouana', 'Ziamma Mansouriah', 'Taher', 'Emir Abdelkader', 'Chekfa', 'Chahna', 'El Milia', 'Sidi Maarouf', 'Settara', 'El Ancer', 'Sidi Abdelaziz', 'Kaous', 'Ghebala', 'Bouraoui Belhadef', 'Djmila', 'Selma Benziada', 'Bousif Ouled Askeur', 'El Kennar Nouchfi', 'Ouled Yahia Khadrouch', 'Boudria Beni Yadjis', 'Kemir Oued Adjoul', 'Texena', 'Djemaa Beni Habibi', 'Bordj Taher', 'Ouled Rabah', 'Ouadjana'),
'19' => array('Sétif', 'Aïn Abessa', 'Aïn Arnat', 'Aïn Azal', 'Aïn El Kebira', 'Aïn Lahdjar', 'Aïn Legraj', 'Aïn Oulmene', 'Aïn Roua', 'Aïn Sebt', 'Aït Naoual Mezada', 'Aït Tizi', 'Amoucha', 'Babor', 'Bazer Sakhra', 'Beidha Bordj', 'Belaa', 'Beni Aziz', 'Beni Chebana', 'Beni Fouda', 'Beni Hocine', 'Beni Mouhli', 'Beni Ouartilane', 'Bir El Arch', 'Bir Haddada', 'Bouandas', 'Bougaa', 'Boutaleb', 'Bousselem', 'Dehamcha', 'Djemila', 'Draa Kebila', 'El Eulma', 'El Ouricia', 'El Ouldja', 'Guellal', 'Guelta Zerka', 'Guenzet', 'Guidjel', 'Hamma', 'Hammam Guergour', 'Hammam Soukhna', 'Harbil', 'Ksar El Abtal', 'Maaouia', 'Maouklane', 'Mezloug', 'Oued El Bared', 'Ouled Addouane', 'Ouled Sabor', 'Ouled Sidi Ahmed', 'Ouled Tebben', 'Rasfa', 'Salah Bey', 'Serdj El Ghoul', 'Sétif', 'Tachouda', 'Talaifacene', 'Taya', 'Tella', 'Tizi N\'Bechar'),
'20' => array('Saïda', 'Aïn El Hadjar', 'Aïn Sekhouna', 'Aïn Soltane', 'Doui Thabet', 'El Hassasna', 'Hounet', 'Maamora', 'Moulay Larbi', 'Ouled Brahim', 'Ouled Khaled', 'Saïda', 'Sidi Ahmed', 'Sidi Amar', 'Sidi Boubekeur', 'Tircine', 'Youb'),
'21' => array('Skikda', 'Aïn Bouziane', 'Aïn Cherchar', 'Aïn Kechra', 'Aïn Zouit', 'Azzaba', 'Bekkouche Lakhdar', 'Benazouz', 'Beni Bachir', 'Beni Oulbane', 'Beni Zid', 'Bin El Ouiden', 'Bouchtata', 'Cheraia', 'Collo', 'Djendel Saadi Mohamed', 'El Ghedir', 'El Hadaik', 'El Harrouch', 'El Marsa', 'Emdjez Edchich', 'Es Sebt', 'Filfila', 'Hamadi Krouma', 'Kanoua', 'Kerkera', 'Kheneg Mayoum', 'Oued Zehour', 'Ouled Attia', 'Ouled Hebaba', 'Ouldja Boulbalout', 'Oum Toub', 'Ramdane Djamel', 'Salah Bouchaour', 'Sidi Mezghiche', 'Skikda', 'Tamalous', 'Zerdazas', 'Zitouna'),
'22' => array('Sidi Bel Abbès', 'Aïn Adden', 'Aïn El Berd', 'Aïn Kada', 'Aïn Thrid', 'Aïn Tindamine', 'Amarnas', 'Badredine El Mokrani', 'Belarbi', 'Benachiba Chelia', 'Ben Badis', 'Bir El Hammam', 'Boudjebaa El Bordj', 'Boukhanafis', 'Chettouane Belaila', 'Dhaya', 'El Hacaiba', 'Hassi Dahou', 'Hassi Zahana', 'Lamtar', 'Makedra', 'Marhoum', 'Mcid', 'Merine', 'Mezaourou', 'Mostefa Ben Brahim', 'Moulay Slissen', 'Oued Sebaa', 'Oued Sefioun', 'Oued Taourira', 'Ras El Ma', 'Redjem Demouche', 'Sehala Thaoura', 'Sfissef', 'Sidi Ali Benyoub', 'Sidi Ali Boussidi', 'Sidi Bel Abbès', 'Sidi Brahim', 'Sidi Chaib', 'Sidi Dahou Zairs', 'Sidi Hamadouche', 'Sidi Khaled', 'Sidi Lahcene', 'Sidi Yacoub', 'Tabia', 'Tafissour', 'Taoudmout', 'Telagh', 'Tenira', 'Tessala', 'Tilmouni', 'Zerouala'),
'23' => array('Annaba', 'Aïn Berda', 'Annaba', 'Berrahal', 'Chetaïbi', 'Cheurfa', 'El Bouni', 'El Hadjar', 'Eulma', 'Oued El Aneb', 'Seraïdi', 'Sidi Amar', 'Treat'),
'24' => array('Guelma', 'Aïn Ben Beida', 'Aïn Hessainia', 'Aïn Larbi', 'Aïn Makhlouf', 'Aïn Reggada', 'Aïn Sandel', 'Belkheir', 'Bendjarah', 'Beni Mezline', 'Bordj Sabath', 'Bouati Mahmoud', 'Bouchegouf', 'Bouhamdane', 'Boumahra Ahmed', 'Dahouara', 'Djeballah Khemissi', 'El Fedjoudj', 'Guelaat Bou Sbaa', 'Guelma', 'Hammam Debagh', 'Hammam N\'Bail', 'Héliopolis', 'Khezara', 'Medjez Amar', 'Medjez Sfa', 'Nechmaya', 'Oued Cheham', 'Oued Fragha', 'Oued Zenati', 'Ras El Agba', 'Roknia', 'Sellaoua Announa', 'Tamlouka'),
'25' => array('Constantine', 'Aïn Abid', 'Aïn Smara', 'Beni Hamiden', 'Constantine', 'Didouche Mourad', 'El Khroub', 'Hamma Bouziane', 'Ibn Badis', 'Ibn Ziad', 'Messaoud Boudjeriou', 'Ouled Rahmoune', 'Zighoud Youcef'),
'26' => array('Médéa', 'Aïn Boucif', 'Aïn Ouksir', 'Aissaouia', 'Aziz', 'Baata', 'Benchicao', 'Beni Slimane', 'Berrouaghia', 'Bir Ben Laabed', 'Boghar', 'Boughezoul', 'Bouaichoune', 'Bououchane', 'Bou Saâda', 'Chahbounia', 'Chellalat El Adhaoura', 'Cheniguel', 'Derrag', 'Deux Bassins', 'Djouab', 'Draa Esmar', 'El Azizia', 'El Guelb El Kebir', 'El Hamdania', 'El Omaria', 'El Ouinet', 'Hannacha', 'Kef Lakhdar', 'Khams Djouamaa', 'Ksar El Boukhari', 'Médéa', 'Meghraoua', 'Medjebar', 'Mezerana', 'Mihoub', 'Ouamri', 'Oued Harbil', 'Ouled Antar', 'Ouled Bouachra', 'Ouled Brahim', 'Ouled Deide', 'Ouled Hellal', 'Ouled Maaref', 'Oum El Djalil', 'Ouzera', 'Rebaia', 'Saneg', 'Sedraia', 'Seghouane', 'Si Mahdjoub', 'Sidi Damed', 'Sidi Errabia', 'Sidi Naamane', 'Sidi Zahar', 'Souagui', 'Tafraout', 'Tablat', 'Tamesguida', 'Tizi Mahdi', 'Tlatet Eddouair', 'Zoubiria'),
'27' => array('Mostaganem', 'Abdelmalek Ramdane', 'Achaacha', 'Aïn Boudinar', 'Aïn Nouissy', 'Aïn Sidi Cherif', 'Aïn Tedles', 'Bouguirat', 'El Hassiane', 'Fornaka', 'Hadjadj', 'Hassi Mameche', 'Khadra', 'Kheireddine', 'Mansourah', 'Mesra', 'Mazagran', 'Mostaganem', 'Nekmaria', 'Oued El Kheir', 'Ouled Boughalem', 'Ouled Maallah', 'Safsaf', 'Sayada', 'Sidi Ali', 'Sidi Belattar', 'Sidi Lakhdar', 'Sirat', 'Souaflia', 'Sour', 'Stidia', 'Tazgait', 'Touahria'),
'28' => array('M\'Sila', 'Aïn El Hadjel', 'Aïn El Melh', 'Aïn Errich', 'Aïn Fares', 'Aïn Khadra', 'Belaiba', 'Ben Srour', 'Beni Ilmane', 'Benzouh', 'Berhoum', 'Bir Foda', 'Bou Saâda', 'Bouti Sayah', 'Chellal', 'Dehahna', 'Djebel Messaad', 'El Hamel', 'El Houamed', 'Hammam Dalaa', 'Khettouti Sed El Djir', 'Khoubana', 'Maadid', 'Maarif', 'Magra', 'M\'Cif', 'Medjedel', 'M\'Sila', 'M\'Tarfa', 'Ouanougha', 'Ouled Addi Guebala', 'Ouled Atia', 'Ouled Derradj', 'Ouled Madhi', 'Ouled Mansour', 'Ouled Sidi Brahim', 'Ouled Slimane', 'Oultène', 'Sidi Aïssa', 'Sidi Ameur', 'Sidi Hadjeres', 'Sidi M\'Hamed', 'Slim', 'Souamaa', 'Tamsa', 'Tarmount', 'Zarzour'),
'29' => array('Mascara', 'Aïn Fares', 'Aïn Fekan', 'Aïn Ferah', 'Aïn Frass', 'Alaimia', 'Aouf', 'Benian', 'Bou Hanifia', 'Bou Henni', 'Chorfa', 'El Bordj', 'El Gaada', 'El Ghomri', 'El Hachem', 'El Keurt', 'El Mamounia', 'El Menaouer', 'Ferraguig', 'Froha', 'Gharrous', 'Gherdjoum', 'Ghriss', 'Guerdjoum', 'Guetna', 'Hacine', 'Khalouia', 'Makdha', 'Mamounia', 'Mascara', 'Matemore', 'Maoussa', 'Mocta Douz', 'Mohammadia', 'Nesmoth', 'Oggaz', 'Oued El Abtal', 'Oued Taria', 'Ras Aïn Amirouche', 'Sedjerara', 'Sehailia', 'Sidi Abdeldjebar', 'Sidi Abdelmoumen', 'Sidi Boussaid', 'Sidi Kada', 'Sig', 'Teghenif', 'Tizi', 'Zahana', 'Zelmata'),
'30' => array('Ouargla', 'Aïn Beida', 'Bennaceur', 'El Borma', 'Hassi Ben Abdellah', 'Hassi Messaoud', 'N\'Goussa', 'Ouargla', 'Rouissat', 'Sidi Khouiled'),
'31' => array('Oran', 'Aïn El Bia', 'Aïn El Kerma', 'Aïn El Turk', 'Arzew', 'Ben Freha', 'Bethioua', 'Bir El Djir', 'Bousfer', 'Boutlelis', 'Bouyakor', 'El Ançor', 'El Braya', 'El Kerma', 'Es Senia', 'Gdyel', 'Hassi Ben Okba', 'Hassi Bounif', 'Hassi Mefsoukh', 'Marsat El Hadjadj', 'Mers El Kébir', 'Messerghin', 'Oran', 'Oued Tlélat', 'Sidi Ben Yebka', 'Sidi Chami', 'Tafraoui'),
'32' => array('El Bayadh', 'Arbaouat', 'Aïn El Orak', 'Boualem', 'Bougtoub', 'Boussemghoun', 'Brezina', 'Cheguig', 'Chellala', 'El Bayadh', 'El Bnoud', 'El Kheiter', 'El Mehara', 'Ghassoul', 'Kef El Ahmar', 'Kraakda', 'Rogassa', 'Sidi Ameur', 'Sidi Slimane', 'Sidi Tifour', 'Stitten', 'Tousmouline'),
'33' => array('Illizi', 'Bordj Omar Driss', 'Debdeb', 'Illizi', 'In Amenas'),
'34' => array('Bordj Bou Arreridj', 'Aïn Taghrout', 'Aïn Tesra', 'Belimour', 'Ben Daoud', 'Bir Kasdali', 'Bordj Bou Arreridj', 'Bordj Ghedir', 'Bordj Zemoura', 'Colla', 'Djaafra', 'El Ach', 'El Anseur', 'El Hamadia', 'El Main', 'El M\'hir', 'Ghilassa', 'Haraza', 'Hasnaoua', 'Khelil', 'Ksour', 'Mansoura', 'Medjana', 'Ouled Brahem', 'Ouled Dahmane', 'Ouled Sidi Brahim', 'Rabta', 'Ras El Oued', 'Sidi Embarek', 'Tafreg', 'Taglait', 'Teniet En Nasr', 'Tesmart', 'Tixter'),
'35' => array('Boumerdès', 'Afir', 'Ammal', 'Baghlia', 'Ben Choud', 'Beni Amrane', 'Bordj Menaïel', 'Boudouaou', 'Boudouaou El Bahri', 'Boumerdès', 'Bouzegza Keddara', 'Chabet El Ameur', 'Corso', 'Dellys', 'Djinet', 'El Kharrouba', 'Hammedi', 'Isser', 'Khemis El Khechna', 'Larbatache', 'Leghata', 'Naciria', 'Ouled Aïssa', 'Ouled Hedadj', 'Ouled Moussa', 'Si Mustapha', 'Sidi Daoud', 'Souk El Had', 'Taourga', 'Thenia', 'Tidjelabine', 'Timezrit', 'Zemmouri'),

'36' => array('El Tarf', 'Bouhadjar', 'Ben Mhidi', 'Bougous', 'El Kala', 'Ain El Assel', 'El Aioun', 'Bouteldja', 'Souarekh', 'Berrihane', 'Lac Des Oiseaux', 'Chefia', 'Drean', 'Chihani', 'Chebaita Mokhtar', 'Besbes', 'Asfour', 'Echatt', 'Zerizer', 'Zitouna', 'Ain Kerma', 'Oued Zitoun', 'Hammam Beni Salah', 'Raml Souk'),
'37' => array('Tindouf', 'Oum El Assel'),
'38' => array('Tissemsilt', 'Bordj Bou Naama', 'Theniet El Had', 'Lazharia', 'Beni Chaib', 'Lardjem', 'Melaab', 'Sidi Lantri', 'Bordj El Emir Abdelkader', 'Layoune', 'Khemisti', 'Ouled Bessem', 'Ammari', 'Youssoufia', 'Sidi Boutouchent', 'Larbaa', 'Maasem', 'Sidi Abed', 'Tamalaht', 'Sidi Slimane', 'Boucaid', 'Beni Lahcene'),
'39' => array('El Oued', 'Robbah', 'Oued El Alenda', 'Bayadha', 'Nakhla', 'Guemar', 'Kouinine', 'Reguiba', 'Hamraia', 'Taghzout', 'Debila', 'Hassani Abdelkrim', 'Hassi Khelifa', 'Taleb Larbi', 'Douar El Ma', 'Sidi Aoun', 'Trifaoui', 'Magrane', 'Beni Guecha', 'Ourmas', 'El Ogla', 'Mih Ouansa'),
'40' => array('Khenchela', 'Mtoussa', 'Kais', 'Baghai', 'El Hamma', 'Ain Touila', 'Taouzianat', 'Bouhmama', 'El Oueldja', 'Remila', 'Cherchar', 'Djellal', 'Babar', 'Tamza', 'Ensigha', 'Ouled Rechache', 'El Mahmal', 'Msara', 'Yabous', 'Khirane', 'Chelia'), 
'41' => array('Souk Ahras', 'Sedrata', 'Hanancha', 'Mechroha', 'Ouled Driss', 'Tiffech', 'Zaarouria', 'Taoura', 'Drea', 'Haddada', 'Khedara', 'Merahna', 'Ouled Moumen', 'Bir Bouhouche', 'Mdaourouche', 'Oum El Adhaim', 'Ain Zana', 'Ain Soltane', 'Quillen', 'Sidi Fredj', 'Safel El Ouiden', 'Ragouba', 'Khemissa', 'Oued Keberit', 'Terraguelt', 'Zouabi'),
'42' => array('Tipaza', 'Menaceur', 'Larhat', 'Douaouda', 'Bourkika', 'Khemisti', 'Aghabal', 'Hadjout', 'Sidi Amar', 'Gouraya', 'Nodor', 'Chaiba', 'Ain Tagourait', 'Cherchel', 'Damous', 'Meurad', 'Fouka', 'Bou Ismail', 'Ahmer El Ain', 'Bou Haroun', 'Sidi Ghiles', 'Messelmoun', 'Sidi Rached', 'Kolea', 'Attatba', 'Sidi Semiane', 'Beni Milleuk', 'Hadjerat Ennous'),
'43' => array('Mila','Ferdjioua', 'Chelghoum Laid', 'Oued Athmenia', 'Ain Mellouk', 'Telerghma', 'Oued Seguen', 'Tadjenanet', 'Benyahia Abderrahmane', 'Oued Endja', 'Ahmed Rachedi', 'Ouled Khalouf', 'Tiberguent', 'Bouhatem', 'Rouached', 'Tessala Lamatai', 'Grarem Gouga', 'Sidi Merouane', 'Tassadane Haddada', 'Derradji Bousselah', 'Minar Zarza', 'Amira Arras', 'Terrai Bainen', 'Hamala', 'Ain Tine', 'El Mechira', 'Sidi Khelifa', 'Zeghaia', 'Elayadi Barbes', 'Ain Beida Harriche', 'Yahia Beniguecha', 'Chigara'),
'44' => array('Ain Defla', 'Miliana', 'Boumedfaa', 'Khemis Miliana', 'Hammam Righa', 'Arib', 'Djelida', 'El Amra', 'Bourached', 'El Attaf', 'El Abadia', 'Djendel', 'Oued Chorfa', 'Ain Lechiakh', 'Oued Djemaa', 'Rouina', 'Zeddine', 'El Hassania', 'Bir Ouled Khelifa', 'Ain Soltane', 'Tarik Ibn Ziad', 'Bordj Emir Khaled', 'Ain Torki', 'Sidi Lakhdar', 'Ben Allal', 'Ain Benian', 'Hoceinia', 'Barbouche', 'Djemaa Ouled Chikh', 'Mekhatria', 'Bathia', 'Tachta Zegagha', 'Ain Bouyahia', 'El Maine', 'Tiberkanine', 'Belaas'),
'45' => array('Naama', 'Mechria', 'Ain Sefra', 'Tiout', 'Sfissifa', 'Moghrar', 'Assela', 'Djeniane Bourzeg', 'Ain Ben Khelil', 'Makman Ben Amer', 'Kasdir', 'El Biod'),
'46' => array('Ain Temouchent', 'Chaabet El Ham', 'Ain Kihal', 'Hammam Bouhadjar', 'Bou Zedjar', 'Oued Berkeche', 'Aghlal', 'Terga', 'Ain El Arbaa', 'Tamzoura', 'Chentouf', 'Sidi Ben Adda', 'Aoubellil', 'El Malah', 'Sidi Boumediene', 'Oued Sabah', 'Ouled Boudjemaa', 'Ain Tolba', 'El Amria', 'Hassi El Ghella', 'Hassasna', 'Ouled Kihal', 'Beni Saf', 'Sidi Safi', 'Oulhaca El Gheraba', 'Tadmaya', 'El Emir Abdelkader', 'El Messaid'),
'47' => array('Ghardaia', 'Dhayet Bendhahoua', 'Berriane', 'Metlili', 'El Guerrara', 'El Atteuf', 'Zelfana', 'Sebseb', 'Bounoura', 'Mansoura'),
'48' => array('Relizane', 'Oued Rhiou', 'Belaassel Bouzegza', 'Sidi Saada', 'Ouled Aiche', 'Sidi Lazreg', 'El Hamadna', 'Sidi Mhamed Ben Ali', 'Mediouna', 'Sidi Khettab', 'Ammi Moussa', 'Zemmoura', 'Beni Dergoun', 'Djidiouia', 'El Guettar', 'Hamri', 'El Matmar', 'Sidi Mhamed Ben Aouda', 'Ain Tarek', 'Oued Essalem', 'Ouarizane', 'Mazouna', 'Kalaa', 'Ain Rahma', 'Yellel', 'Oued El Djemaa', 'Ramka', 'Mendes', 'Lahlef', 'Beni Zentis', 'Souk El Haad', 'Dar Ben Abdellah', 'El Hassi', 'Had Echkalla', 'Bendaoud', 'El Ouldja', 'Merdja Sidi Abed', 'Ouled Sidi Mihoub'),
'49' => array('El Mghair', 'Djamaa', 'Oum Touyour', 'Sidi Amrane', 'Sidi Khelil', 'Still', 'Tendla'),
'50' => array('El Menia', 'Hassi Gara'),
'51' => array('Ouled Djellal', 'Besbes', 'Ech Chaiba', 'Doucen', 'Sidi Khaled'),
'52' => array('Bordj Baji Mokhtar', 'Timiaouine'),
'53' => array('Béni Abbès', 'El Ouata', 'Igli', 'Kerzaz', 'Ksabi', 'Ouled Khoudir', 'Tabelbala', 'Tamtert'),
'54' => array('Timimoun', 'Aougrout', 'Charouine', 'Deldoul', 'Metarfa', 'Ouled Aissa', 'Ouled Said', 'Talmine', 'Tinerkouk'),
'55' => array('Touggourt', 'Benaceur', 'Blidet Amor', 'El Hadjira', 'El Allia', 'Megarine', 'M\'Naguer', 'Nezla', 'Sidi Slimane', 'Taibet', 'Tamacine', 'Tebesbest', 'Zaouia El Abidia'),
'56' => array('Djanet', 'Bordj El Haoues'),
'57' => array('In Salah', 'Foggaret Ezzaouia', 'In Ghar'),
'58' => array('In Guezzam', 'In Tin Zouatine')   
 );
    
    if (!empty($state_code) && isset($cities[$state_code])) {
        return $cities[$state_code];
    }
    
    return $cities;
}