@-webkit-keyframes infinite-spinning {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes infinite-spinning {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes pulse {
  from {
    background: #fffdc3;
  }

  to {
    background: #fff;
  }
}

@keyframes pulse {
  from {
    background: #fffdc3;
  }

  to {
    background: #fff;
  }
}

.at-repeating-fields a {
  outline: 0;
}

.at-repeating-fields .loading {
  position: relative;
}

.at-repeating-fields .loading:after {
  font-family: dashicons;
  font-size: 20px;
  content: "\f463";
  position: absolute;
  bottom: 15px;
  left: 15px;
  color: #232323;
  -webkit-animation: infinite-spinning 0.5s infinite;
  animation: infinite-spinning 0.5s infinite;
}

.at-repeating-fields .at-repeating-add-field {
  margin-bottom: 15px;
}

.at-repeating-fields .at-repeating-remove-action {
  position: relative;
  text-align: right;
  z-index: 100;
}

.at-repeating-fields .dashicons {
  font-size: 16px;
  position: relative;
  top: 5px;
  left: -4px;
}

.at-repeating-fields .dashicons-dismiss {
  color: red;
}

.at-repeating-fields .post-field {
  background: #fff;
  padding: 15px;
  margin-bottom: 15px;
  border: solid 1px #ddd;
  border-radius: 2px;
  cursor: move;
  -webkit-animation: pulse 1.5s;
  animation: pulse 1.5s;
}

.at-repeating-fields .post-field:hover {
  border-color: #ccc;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.at-repeating-fields .post-field.loading {
  background: #fffdc3;
}

.at-repeating-fields label {
  display: block;
}

.at-repeating-fields .post-field-item {
  margin-top: 10px;
}

.at-repeating-fields .post-field select {
  margin-top: 5px;
}

.at-repeating-fields .ui-sortable-helper {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
}

.at-repeating-fields .ui-state-highlight {
  background: #f1f1f1;
  height: 173px;
  border: dotted 2px #ccc;
  border-radius: 2px;
  margin-bottom: 15px;
}

.at-repeating-fields .inner input,
.at-repeating-fields .inner textarea {
  display: block;
  margin: 5px 0 10px 0;
}

.at-repeating-fields .inner textarea {
  width: 100%;
  height: 150px;
}

.at-repeating-fields .inner .ui-state-highlight {
  height: 111px;
}

.at-repeating-fields .inner .wp-picker-input-wrap input {
  margin: 0;
  display: none;
}

.at-repeating-fields .inner .wp-picker-open + .wp-picker-input-wrap input {
  display: inline-block !important;
  margin-left: 4px;
}