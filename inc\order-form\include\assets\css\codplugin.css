#codplugin-checkout{
    position: relative;
    padding: 30px;
    border: 2px solid #259bea;
    border-radius: 6px;
    margin-top: 50px;
}
.codplugin-checkout-title {    
    text-align: center;
}
.codplugin-checkout-title h3{
    padding: 0 20px;
    display: inline-block;
    color: #000;
    font-size: 16px;
}
#codplugin_woo_single_form input,
#codplugin_state,
#codplugin_city {
    border: 2px solid #bce0f7;
    height: 50px;
}



#codplugin_woo_single_form input:valid,
#codplugin_woo_single_form select:valid {
    border: 2px solid #69bf29 !important;
    outline: none;
}

#codplugin_woo_single_form input:required:focus:invalid,
#codplugin_woo_single_form select:required:focus:invalid {
    border: 2px solid #d65d67 !important;
    outline: none;
}

.codplugin-field {
    margin: 0;
}

#codplugin_state .variations td.value {
    width: initial;
}

#codplugin_woo_single_form {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: repeat(2, 1fr);
}
#codplugin-checkout .form-footer {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: 125px 1fr;
    margin-top: 10px;
}
#nrwooconfirm{
    background-color: #259bea;
    border: none;
    color: #fff !important;
    height: 50px;
    line-height: 50px;
    padding: 0;
    overflow: hidden;
}
#nrwooconfirm.atc-buy-button {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: repeat(2, 1fr);
    background: none !important;
}
#nrwooconfirm.atc-buy-button .button,
#nrwooconfirm.atc-buy-button input {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    white-space: normal;
    padding: 3px;
    font-size: 15px;
}
#nrwooconfirm.atc-buy-button .button,
#nrwooconfirm.atc-buy-button .wc-forward {
    padding: 10px;
}
#codplugin_add_button,#codplugin_count_button,#codplugin_remove_button{
    color: black;
    font-size: 20px;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
    line-height: 46px;
}
#codplugin_woo_radio,
#codplugin-checkout .variation-prices input[type="radio"]{
	width: 15px!important;
	height: 15px!important;
}


#codplugin_state,
#codplugin_city {
    cursor: pointer;
    color: #888;
}

span#codplugin_h_o {
    line-height: 50px;
    font-size: 16px;
}
span#codplugin_h_o i {
    font-size: 20px;
    color: #bce0f7;
    margin-right: 5px;
}
div#codplugin_h_left {
    float: right;
} 
div#codplugin_h_left i {
    color: #bce0f7;
    line-height: 50px;
    font-size: 25px;
}
div#codplugin_h_right {
  float: left;
}
div#codplugin_order_history {
    height: 50px;
    cursor: pointer;
    border-bottom: 2px solid #bce0f7;
    background: #f0f9ff;
    padding: 0 10px;
    margin-bottom: 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

#codplugin_gif{
    margin: auto;
    width: 50px;
    height: 50px;
    display: none;
}

.form-qte {
    display: grid;
    grid-template-columns: 35px 50px 35px;
    border: 2px solid #eae7f7;
    border-radius: 3px;
}
#codplugin_remove_button {
    border-left: 2px solid #bce0f7;
    border-right: 0;
}
#codplugin_add_button {
    border-right: 2px solid #bce0f7;
    border-left: 0;
}
#codplugin-checkout table {
    border: none;
}

#codplugin-checkout .variation-prices {
    font-size: 16px;
}
#codplugin-checkout .variation-prices bdi {
font-weight: bold;
    color: #259bea;
}

#codplugin-checkout .variation-prices  .crossed-price bdi {
    font-weight: normal;
    color: #aeaeae;
}
#codplugin-checkout td {
    border: none;
    border-bottom: 2px dotted #bce0f7;
}
#codplugin-checkout .full-price td {
    font-weight: bold;
    border-bottom: 0;
}
#codplugin-checkout .full-price td:last-child {
    color: #259bea;

}
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
    background-size: 9px;
    background-image: url(data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E);
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    padding-right: 28px !important;
}
table {
    border-collapse: separate;
    border-spacing: 0;
    border-width: 1px 0 0 1px;
    margin: 0 0 1.5em;
    width: 100%;
}
table, td, th {
    border: 1px solid rgba(0,0,0,.1);
}
#codplugin-checkout .woocommerce-variation-add-to-cart {
    display: none !important;
}

.woocommerce div.product .product_meta {
    border-top: none!important;
}


#cod-upsell{
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 25; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
#cod-upsell-box{
    background-color: #fff;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
}

#cod-upsell-loader{
    display: none;
}

#cod-upsell-box .cod-upsell-heading {
    color: red;
    text-align: center;
    font-weight: bold;
    font-size: 45px;
    margin: 0;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 3px dotted #eee;
}
.cod-upsell-product-title {
  text-align: center;
  font-size: 24px;
  color: #000;
}

.cod-upsell-product img {
    margin: 20px auto;
    display: block;
}

.cod-upsell-product .price {
    display: block;
    text-align: center;
    color: red;
    margin-bottom: 25px;
}
#upsell-submit button {
  display: block;
  margin: 20px auto;
  color: #fff;
  background-color: #4caf50;
  padding: 15px 50px;
  font-size: 30px;
  border: none;
}
#upsell-submit #cod-upsell-cancel {
    background-color: transparent;
    color: #bababa;
    padding: 0;
    font-size: 20px;
}

#upsell-submit button:hover {
    cursor: pointer;
}
#upsell-submit #cod-add-upsell:hover {
  background-color: #222;
}
#upsell-submit #cod-upsell-cancel:hover {
  color: #555;
}

#codplugin-checkout .chosen-search-input,
#codplugin-checkout .chosen-search input {
    width: 100% !important;
    box-sizing: border-box;
    margin: 0;
    padding: 0 10px;
    border: none;
    height: 30px;
}

#codplugin-checkout .chosen-single {
    padding: 16px;
    border: 2px solid #bce0f7;
    height: 50px;
    background: none;
    background-color: #fff !important;
    border-radius: 3px;
    box-shadow: none;
    color: #919191;
}

#codplugin-checkout .chosen-container-single .chosen-single div {
    top: 10px;
}

#codplugin-checkout .chosen-container .chosen-drop {
    border: 2px solid #bce0f7;
    border-top: none;
    margin-top: -13px;
}

#codplugin-checkout .chosen-container {
    width: 100% !important;
    font-size: 14px;
}
#codplugin-checkout .chosen-container-single .chosen-single span,
#codplugin-checkout .chosen-container-single .chosen-single div {
    margin: 0;
}

#codplugin-checkout .chosen-container .chosen-results li.no-results {
    display: none;
}

#codplugin-checkout .chosen-container .chosen-results li.disabled-result {
    display: none;
}

#codplugin-checkout .chosen-container .chosen-results li {
    line-height: 24px;
}
#codplugin_show_hide {
    background: #f0f9ff;
    padding: 10px;
    margin-top: 0;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}

#codplugin_show_hide table {
    margin-bottom: 0;
}

#codplugin_count_number {
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 3px;
    background-color: #259bea;
    border-radius: 3px;
    color: #fff;
    font-weight: bold;
}
#codplugin_count_number:before {
    content: 'x ';
}
#codplugin_d_free span {
    display: inline-block;
    font-size: 15px;
    height: 20px;
    line-height: 20px;
    padding: 0 7px;
    background-color: #259bea;
    border-radius: 3px;
    color: #fff;
}
.summary-product-title {
    font-weight: bold !important;
}
.summary-select-state {
    font-size: 14px;
    opacity: .8;
}
.codplugin_currency {
    display: none;
}

#shipping-methods {
    font-size: 14px;
    margin-left: 10px;
    opacity: .75;
}
.blink-me {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.whatsapp-order-section {
    margin: 0 auto 15px;
    text-align: center;
}
.whatsapp-order-section #whatsapp-orders {
    color: #fff;
    display: inline-block;
    background: #25d466;
    border-radius: 50px;
    padding: 10px;
    border: none;
    font-weight: bold;
    font-size: 15px;
}

.whatsapp-order-section #whatsapp-orders:hover {

    cursor: pointer;
}
.radio-variation-prices {
    margin-top: 10px;
}

.radio-variation-prices tbody {    
    display: grid;
    background: #f1f9ff;
    padding: 10px;
    border-radius: 3px;
}
.radio-variation-prices tr {
    position: relative;
    border: 2px solid #bce0f7;
    margin: 3px 0;
    border-radius: 5px;
    padding: 5px 15px;
    background: white;
    grid-column-gap: 0.5rem;
    flex-direction: row;
    align-items: center;
    display: flex;
    cursor: pointer;
}
#codplugin-checkout .radio-variation-prices td {
    border: none;
}
.radio-variation-prices tr td:last-child {
    margin-left: auto;
}

.radio-variation-prices input[type="radio"] {
    position : relative;
}
.radio-variation-prices  input[type="radio"]:before {
    content: '';
    width: 30px;
    height: 30px;
    border-radius: 30px;
    display: inline-block;
    background: #fff;
    border: 2px solid #ddd;
    transition: all .1s ease-in-out 0s;
    margin-top: -5px;
    margin-left: -5px;
}
.radio-variation-prices  input[type="radio"]:checked::before {
    width: 30px;
    height: 30px;
    border-color: #259bea;
    content: "";
    text-align: center;
    display: inline-block;
    background: #259bea;
    color: white;
    
}
.radio-variation-prices  input[type="radio"]:checked::after {
    content: '';
    display: block;
    position: absolute;
    top: 0px;
    left: 6px;
    width: 9px;
    height: 17px;
    border: 2px  solid white;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}
.radio-variation-prices tr.checked-var {
    border-color: #259bea !important;
}
.checked {
    position: relative;
}
.checked::after {
    content: "";
    position: absolute;
    top: -7px;
    right: 10px;
    display: inline-block;
    padding: 0 20px;
    line-height: 18px;
    background-color: #259bea;
    color: #fff;
    border-radius: 10px;
    font-size: 0.8em;
    vertical-align: middle;
}

html[lang="ar"] .checked::after {
    content: "";
}

html[lang="fr-FR"] .checked::after {
    content: "";
}

.variation-prices:not(.radio-variation-prices) .checked::after { 
    top: 13px;
}

.sold-individual .form-qte {display :none;} 
#codplugin-checkout .sold-individual.form-footer {grid-template-columns: 1fr;}

#codplugin-thanks {
    display :none;
    width: 100%;
    z-index: 98;
    background-color: #fff;

}

.elementor-page #codplugin-thanks {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
#codplugin-thanks-box {
    background-color: #ffffff;
    padding: 20px;
    width: 550px;
    margin: 50px auto;
    z-index: 99;
}

#thanks-order-summary #shipping-methods {
    display: none;
}
.thanks-box-title {
    text-align: center;
    font-size: 18px;
    margin: 20px 0;
}

.thanks-box-content {
    margin-bottom: 30px;
}
.order-summary-title {
    line-height: 50px;
    padding: 0 10px;
    font-weight: bold;
    background: #259bea;
    color: #fff;
}

#codplugin-thanks-box #codplugin_show_hide {
    display:block;
    background: transparent;
    border: 1px solid #259bea;
    padding: 0;
    border-radius: 0;
}
#codplugin-thanks-box table, 
#codplugin-thanks-box td, 
#codplugin-thanks-box th {
    border: none;
}
#codplugin-thanks-box td {
    padding: 8px;
    background: #f8f8f8;
    border-top: 1px solid #DDDDDD;
}
#codplugin-thanks-box tr:first-child td {
    border-top: none;
    background: #fff;
}


@keyframes shaking {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  4%, 46% {
    transform: translate3d(-1px, 0, 0);
  }
  8%, 42% {
    transform: translate3d(2px, 0, 0);
  }
  12%, 37% {
    transform: translate3d(-3px, 0, 0);
  }
  16%, 33% {
    transform: translate3d(3px, 0, 0);
  }
}


/* -----------------------------------------
     Global Mediaqueries
----------------------------------------- */

@media (max-width: 1349px) and (min-width: 992px) {

    #codplugin-checkout {
        padding: 30px 10px;
    }

}

@media (max-width: 767px) {

    #codplugin-checkout {
        padding: 30px 10px;
    }
    
    #upsell-submit button {
        padding: 12px 25px;
        font-size: 20px;
    }
    #cod-upsell-box .cod-upsell-heading {
        font-size: 36px;
    }
    #codplugin-thanks-box {
        width: 90%;
    }
}

.rtl .radio-variation-prices tr td:last-child {
    margin-right: auto;
    margin-left: 0;
}
.rtl .radio-variation-prices input[type="radio"]:checked::after {
    right: 6px;
    left: auto;
}
.rtl div#codplugin_h_left {
    float: left;
}
.rtl div#codplugin_h_right {
    float: right;
}
.rtl input#nrwooconfirm {
    float: left;
}
.rtl .form-qte {
    float: right;
}

.rtl #codplugin_add_button {
    border-left: 2px solid #bce0f7;
    border-right: 0;
}

.rtl #codplugin_remove_button {
    border-right: 2px solid #bce0f7;
    border-left: 0;
}

.rtl #codplugin-checkout .chosen-container-single .chosen-single div {
    right: auto;
    left: 10px;
}
.rtl #codplugin-checkout .chosen-search-input,
.rtl #codplugin-checkout .chosen-search input {
    background-position-x: -29px;
 }

.rtl #shipping-methods {
    margin-left: 0px;
    margin-right: 10px;
}

.rtl .radio-variation-prices input[type="radio"]:before {
    margin-left: auto;
    margin-right: -5px;
}
.rtl .checked::after {
    left: 10px;
    right: auto;
}

@media (max-width: 488px) {

    #codplugin-checkout .variation-prices {
        font-size: 14px;
    }
    .codplugin-checkout-title h3 {
        padding: 0 10px;
        margin-bottom: 15px;
    }
    div#codplugin_order_history, #codplugin_show_hide {
        font-size: 14px;
    }
    #codplugin-checkout td,
    #codplugin_show_hide {
        padding: 5px;
    }
    .variation-prices:not(.radio-variation-prices) .checked::after { 
        top: 7px;
    }
    #codplugin-checkout .variation-prices .woocommerce-Price-amount {
        display: block;
        font-size: 14px;
    }
    #codplugin_woo_single_form {
        grid-gap: 13px;
        grid-template-columns: repeat(1, 1fr);
    }
    #codplugin-checkout {
        padding: 10px 10px 10px;
    }
    #nrwooconfirm, .form-qte {
        margin: 0;
    }
    .codplugin-checkout-title {
        position: relative; 
    }
    div#codplugin_order_history {
        margin-top: 10px;
        margin-left: 0;
        margin-right: 0;
        padding: 0 5px;
    }
    #codplugin_show_hide {
        margin-left: 0;
        margin-right: 0;        
    }
    #codplugin_show_hide table tr td:last-child {
        text-align: right;
    }
   
}

@media (max-width: 417px ) {
    #nrwooconfirm.atc-buy-button {
        grid-gap : 10px;
        grid-template-columns: 1fr;
        height: auto;
   }
   #nrwooconfirm.atc-buy-button .button, #nrwooconfirm.atc-buy-button input {
    height: 45px;
   }
    .form-qte {
        grid-template-columns: 1fr 1fr 1fr;
    }
    #codplugin-checkout .form-footer {
        grid-gap: 10px;
        grid-template-columns: 1fr;
    }


}










