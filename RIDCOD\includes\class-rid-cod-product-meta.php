<?php
/**
 * Product Meta Box Handler for RID COD Plugin
 * Handles the product-level settings for showing/hiding the order form
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class RID_COD_Product_Meta {

    /**
     * Constructor
     */
    public function __construct() {
        // Always add hooks, but check the setting in the methods
        add_action('add_meta_boxes', array($this, 'add_product_meta_box'));
        add_action('save_post', array($this, 'save_product_meta'));

        // Add admin notice if feature is disabled but user is on product edit page
        add_action('admin_notices', array($this, 'maybe_show_feature_disabled_notice'));
    }

    /**
     * Add meta box to product edit page
     */
    public function add_product_meta_box() {
        // Only add meta box if the product control feature is enabled
        if (get_option('rid_cod_enable_product_control', 'no') === 'yes') {
            add_meta_box(
                'rid_cod_product_settings',
                __('RID COD Order Form Settings', 'rid-cod'),
                array($this, 'render_meta_box'),
                'product',
                'side',
                'default'
            );
        }
    }

    /**
     * Render the meta box content
     */
    public function render_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('rid_cod_product_meta_nonce', 'rid_cod_product_meta_nonce');

        // Get current value
        $show_form = get_post_meta($post->ID, '_rid_cod_show_form', true);
        
        // Default to 'yes' if not set (as per requirements)
        if ($show_form === '') {
            $show_form = 'yes';
        }

        ?>
        <div class="rid-cod-product-meta">
            <p>
                <label for="rid_cod_show_form">
                    <strong><?php _e('Show Order Form:', 'rid-cod'); ?></strong>
                </label>
            </p>
            <p>
                <select name="rid_cod_show_form" id="rid_cod_show_form" style="width: 100%;">
                    <option value="yes" <?php selected($show_form, 'yes'); ?>>
                        <?php _e('Yes - Show order form', 'rid-cod'); ?>
                    </option>
                    <option value="no" <?php selected($show_form, 'no'); ?>>
                        <?php _e('No - Hide order form', 'rid-cod'); ?>
                    </option>
                </select>
            </p>
            <p class="description">
                <?php _e('Choose whether to display the RID COD order form on this product page. Default is "Yes".', 'rid-cod'); ?>
            </p>
            
            <?php if (get_option('rid_cod_enable_product_control', 'no') !== 'yes') : ?>
                <p class="description" style="color: #d63638;">
                    <strong><?php _e('Note:', 'rid-cod'); ?></strong>
                    <?php _e('Product-level control is currently disabled in RID COD settings. Enable it in the Anti-Spam settings to use this feature.', 'rid-cod'); ?>
                </p>
            <?php endif; ?>
        </div>
        </div>

        <style>
            .rid-cod-product-meta {
                padding: 10px 0;
            }
            .rid-cod-product-meta label {
                display: block;
                margin-bottom: 5px;
            }
            .rid-cod-product-meta select {
                margin-bottom: 10px;
            }
        </style>
        <?php
    }

    /**
     * Save meta box data
     */
    public function save_product_meta($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check if user has permission to edit this post
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check nonce for security
        if (!isset($_POST['rid_cod_product_meta_nonce']) || 
            !wp_verify_nonce($_POST['rid_cod_product_meta_nonce'], 'rid_cod_product_meta_nonce')) {
            return;
        }

        // Check if this is a product
        if (get_post_type($post_id) !== 'product') {
            return;
        }

        // Only save if the product control feature is enabled
        if (get_option('rid_cod_enable_product_control', 'no') !== 'yes') {
            return;
        }

        // Save the meta value
        if (isset($_POST['rid_cod_show_form'])) {
            $show_form = sanitize_text_field($_POST['rid_cod_show_form']);
            
            // Validate the value
            if (in_array($show_form, array('yes', 'no'))) {
                update_post_meta($post_id, '_rid_cod_show_form', $show_form);
            }
        }
    }

    /**
     * Check if the form should be shown for a specific product
     *
     * @param int $product_id The product ID
     * @return bool True if form should be shown, false otherwise
     */
    public static function should_show_form($product_id) {
        // If product control is disabled, always show the form (default behavior)
        if (get_option('rid_cod_enable_product_control', 'no') !== 'yes') {
            return true;
        }

        // Validate product ID
        if (!$product_id || !get_post($product_id) || get_post_type($product_id) !== 'product') {
            return true; // Default to showing form for invalid products
        }

        // Get the product setting
        $show_form = get_post_meta($product_id, '_rid_cod_show_form', true);

        // Default to 'yes' if not set (as per requirements)
        if ($show_form === '') {
            $show_form = 'yes';
        }

        return $show_form === 'yes';
    }

    /**
     * Check if the form should be shown for the current product (if on a product page)
     *
     * @return bool True if form should be shown, false otherwise
     */
    public static function should_show_form_current_product() {
        global $product;

        if (!$product || !is_product()) {
            return true; // Default behavior for non-product pages
        }

        return self::should_show_form($product->get_id());
    }

    /**
     * Get all products where the form is hidden (for admin purposes)
     * 
     * @return array Array of product IDs where form is hidden
     */
    public static function get_products_with_hidden_form() {
        global $wpdb;

        $results = $wpdb->get_col($wpdb->prepare("
            SELECT post_id 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = %s 
            AND meta_value = %s
        ", '_rid_cod_show_form', 'no'));

        return array_map('intval', $results);
    }

    /**
     * Show admin notice if feature is disabled but user is on product edit page
     */
    public function maybe_show_feature_disabled_notice() {
        global $pagenow, $post;

        // Only show on product edit pages
        if ($pagenow !== 'post.php' && $pagenow !== 'post-new.php') {
            return;
        }

        // Only for products
        if (!$post || get_post_type($post) !== 'product') {
            return;
        }

        // Only if feature is disabled
        if (get_option('rid_cod_enable_product_control', 'no') === 'yes') {
            return;
        }

        // Check if user can manage options (to see the settings)
        if (!current_user_can('manage_options')) {
            return;
        }

        $settings_url = admin_url('admin.php?page=rid-cod-settings&tab=antispam');
        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong><?php _e('RID COD:', 'rid-cod'); ?></strong>
                <?php
                printf(
                    __('Product-level form control is currently disabled. You can enable it in the %s to control which products show the order form.', 'rid-cod'),
                    '<a href="' . esc_url($settings_url) . '">' . __('Anti-Spam settings', 'rid-cod') . '</a>'
                );
                ?>
            </p>
        </div>
        <?php
    }
}
