@charset "UTF-8";

/*!
Theme Name: napoleon
Theme URI: https://www.bitherhood.com/
Author: bitherhood
Author URI: https://www.bitherhood.com/
Description: An cash on delivery theme for WordPress
Version: 1.5.4
Tested up to: 5.8
Requires PHP: 5.6
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: napoleon

This theme is inspired & based on Nozama theme made by CSSIgniter

*/

/* -----------------------------------------
  Table of Contents
--------------------------------------------

.. 01. General
.. 02. Main Navigation
.. 03. Header
.. 04. Hero Area
.. 05. Modules
.. 06. Footer
.. 07. Comments
.. 08. Widgets Styling
.. 09. WordPress defaults
.. 10. Mobile Menu
.. 11. WooCommerce
.. 12. External Plugins
.. 13. Grid Effects
.. 14. Utilities
.. 15. Global Mediaqueries
*/

@-webkit-keyframes rot {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes rot {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

/* -----------------------------------------
  01. General
----------------------------------------- */

html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: transparent;
}

* {
  box-sizing: inherit;
}

*::before,
*::after {
  box-sizing: inherit;
}

[tabindex="-1"]:focus {
  outline: none !important;
}

[hidden] {
  display: none !important;
}

/* Basic Typography
=================================== */


body,
.navigation-main > li[class*="fa-"],
.mm-listview > li[class*="fa-"],
.widget_nav_menu li[class*="fa-"] {
  font-family: "Source Sans Pro", sans-serif;
}
.woocommerce-thankyou-order-details li strong,
.wc-bacs-bank-details li strong,
.woocommerce-EditAccountForm legend {
  font-family: "Source Sans Pro", sans-serif;
}
body {
  line-height: 1.5;
  font-size: 16px;
  background-color: #fff;
  color: #4a4a4a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: normal;
  margin: 0 0 35px;
  text-rendering: optimizeLegibility;
  letter-spacing: -0.01em;
  font-weight: 400;
}

h1 {
  font-size: 28px;
}

h2 {
  font-size: 26px;
}

h3 {
  font-size: 24px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

p {
  margin: 0 0 15px;
}

img {
  display: inline-block;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

a {
  -webkit-transition: color 0.18s ease, background-color 0.18s ease,
    border-color 0.18s ease;
  transition: color 0.18s ease, background-color 0.18s ease,
    border-color 0.18s ease;
  color: #ea593c;
  text-decoration: none;
}

a:hover {
  color: #12c5ff;
  text-decoration: none;
}

a:focus {
  outline: 1px dotted #259bea;
}

.group::after,
.clear::after {
  content: "";
  display: table;
  clear: both;
}

a,
area,
button,
[role="button"],
input,
label,
select,
summary,
textarea {
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

hr {
  margin: 45px 0;
  border: 0;
  border-bottom: 1px solid #dddddd;
}

/* General Element Styling
=================================== */

/* Reset figure margin from normalize.css */

figure {
  margin: 0;
}

/* Lists */

ul,
ol {
  padding-left: 20px;
}

ul {
  list-style: disc;
}

ol {
  list-style: decimal;
}

dl {
  margin: 0 0 20px;
}

dt {
  font-weight: bold;
}

dd {
  margin: 0 0 15px;
}

/* Blockquotes */

blockquote {
  margin: 20px 0;
  padding-left: 15px;
  border-left: 3px solid #dddddd;
  font-size: 17px;
  font-weight: 300;
}

blockquote cite {
  display: block;
  font-weight: bold;
  font-style: italic;
  margin: 10px 0 0;
  color: rgba(74, 74, 74, 0.8);
  font-size: 14px;
}

/* Tables */

table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.entry-content table,
.comment-content table {
  border-width: 1px 0 0 1px;
  margin-bottom: 24px;
}

.entry-content th,
.entry-content td,
.comment-content th,
.comment-content td {
  border-bottom: 1px solid #dddddd;
}

.entry-content th:first-child,
.entry-content td:first-child,
.comment-content th:first-child,
.comment-content td:first-child {
  padding-left: 0;
}

.entry-content caption,
.entry-content th,
.entry-content td,
.comment-content caption,
.comment-content th,
.comment-content td {
  font-weight: normal;
  text-align: left;
  padding: 10px 5px 10px;
  vertical-align: middle;
}

.entry-content th,
.comment-content th {
  border-width: 0 1px 1px 0;
  font-weight: 600;
}

.entry-content td,
.comment-content td {
  border-width: 0 1px 1px 0;
}

/* Code */

code,
kbd,
tt,
var,
samp,
pre {
  font-family: monospace, serif;
  -webkit-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  font-style: normal;
}

pre {
  padding: 10px;
  border-radius: 3px;
  background-color: #f6f9fc;
  display: block;
  margin: 30px 0;
  overflow: auto;
}

/* Various */

address {
  margin: 0 0 15px;
  font-style: normal;
  line-height: inherit;
}

abbr,
abbr[title] {
  text-decoration: none;
  border: 0;
}

mark {
  color: inherit;
  font: inherit;
  background: none;
}

/* Embeds and iframes
=================================== */

embed,
iframe,
object,
video,
audio {
  margin-bottom: 15px;
  max-width: 100%;
  border: 0;
}

p > embed,
p > iframe,
p > object,
p > audio,
p > video,
span > embed,
span > iframe,
span > object,
span > audio,
span > video {
  margin-bottom: 0;
}

#map *,
.map * {
  max-width: none !important;
}

/* General Form Styling
=================================== */

label {
  display: block;
  margin: 0 0 5px;
  font-weight: normal;
}

input,
textarea,
select {
  display: inline-block;
  width: 100%;
  max-width: 100%;
  height: 35px;
  padding: 6px 12px;
  box-shadow: none;
  line-height: normal;
  border: 2px solid #e1e5e5;
  background-color: #fff;
  background-image: none;
  border-radius: 3px;
  font-size: 14px;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-clip: padding-box;
  background-size: 9px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  padding-right: 28px;
}

select::-ms-expand {
  background-color: transparent;
  border: 0;
}

input[type="search"] {
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

textarea {
  height: auto;
  resize: vertical;
  padding-top: 10px;
  padding-bottom: 10px;
}

select {
  max-width: 100%;
}

input[type="checkbox"],
input[type="radio"] {
  margin: 4px 0 0;
  line-height: normal;
  width: auto;
  height: auto;
}

fieldset {
  margin: 0 0 15px;
  padding: 0;
  border: 0;
  min-width: 0;
}

/* Placeholder text color */

::-webkit-input-placeholder {
  font-weight: normal;
  opacity: 0.8;
}

:-moz-placeholder {
  font-weight: normal;
  opacity: 0.8;
}

::-moz-placeholder {
  font-weight: normal;
  opacity: 0.8;
}

:-ms-input-placeholder {
  font-weight: normal;
  opacity: 0.8;
}

/* Buttons
=================================== */

.btn,
.button,
.comment-reply-link,
.added_to_cart,
input[type="submit"],
input[type="reset"],
button[type="submit"] {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  text-transform: none;
  color: #fff;
  background-color: #259bea;
  border: 0;
  font-family: inherit;
  border-radius: 3px;
  font-weight: 600;
  padding: 13px 28px;
  font-size: 16px;
}

.btn.disabled,
.btn:disabled,
.button.disabled,
.button:disabled,
.comment-reply-link.disabled,
.comment-reply-link:disabled,
input[type="submit"].disabled,
input[type="submit"]:disabled,
input[type="reset"].disabled,
input[type="reset"]:disabled,
button[type="submit"].disabled,
button[type="submit"]:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

.btn:hover,
.button:hover,
.comment-reply-link:hover,
input[type="submit"]:hover,
input[type="reset"]:hover,
button[type="submit"]:hover {
  text-decoration: none;
  color: #fff;
  background-color: #52dbee;
}

.btn:focus,
.button:focus,
.comment-reply-link:focus,
input[type="submit"]:focus,
input[type="reset"]:focus,
button[type="submit"]:focus {
  outline: 0;
}

.btn-sm {
  padding: 9px 22px;
  font-size: 14px;
}

.btn-lg {
  padding: 18px 38px;
  font-size: 16px;
}

.btn-transparent {
  background-color: transparent;
  border: 1px solid #259bea;
  color: #259bea;
}

.btn-transparent:hover {
  color: #fff;
  border-color: #259bea;
  background-color: #259bea;
}

.btn-block {
  min-width: 100%;
  display: block;
  padding-left: 20px;
  padding-right: 20px;
}

/* Magnific Popup Overrides
=================================== */

.mfp-bg {
  background-color: #000;
}

.mfp-preloader {
  color: #fff;
}

.mfp-preloader a {
  color: #fff;
}

.mfp-preloader a:hover {
  color: #fff;
}

.mfp-container:not(.mfp-s-error) .mfp-preloader {
  border: 6px solid rgba(255, 255, 255, 0.35);
  border-top-color: rgba(255, 255, 255, 0.875);
  border-radius: 100%;
  height: 40px;
  width: 40px;
  -webkit-animation: rot 0.8s infinite linear;
  animation: rot 0.8s infinite linear;
  background-color: transparent;
  text-indent: -999em;
  margin: 0 auto;
}

button.mfp-close,
button.mfp-arrow {
  border: 0;
  opacity: 1;
}

button.mfp-close:hover,
button.mfp-arrow:hover {
  background: none;
  border: 0;
}

.mfp-close-btn-in .mfp-close {
  color: #fff;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: #fff;
}

.mfp-arrow {
  line-height: 0.3;
}

.mfp-arrow::before,
.mfp-arrow::after {
  border: 0;
}

.mfp-arrow::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 70px;
  color: #fff;
}

.mfp-arrow-right::after,
.mfp-arrow-right .mpf-a {
  content: "\f105";
}

.mfp-arrow-left::after,
.mfp-arrow-left .mpf-a {
  content: "\f104";
}

/* -----------------------------------------
  02. Main Navigation
----------------------------------------- */

.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.navigation-main,
.navigation-top {
  margin: 0;
  padding: 0;
  list-style: none;
  line-height: normal;
}

.navigation-main::after {
  content: "";
  display: table;
  clear: both;
}

.head-mast [class^="col-lg-12"] .navigation-main {
  margin-top: 10px;
}

.navigation-main li {
  position: relative;
}

.navigation-main > li {
  display: inline-block;
}

.navigation-main a {
  display: block;
  white-space: nowrap;
}

.navigation-main ul {
  text-align: left;
  position: absolute;
  z-index: 10;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.25s ease 0.2s, visibility 0s ease 0.35s,
    -webkit-transform 0.25s ease 0.2s;
  transition: opacity 0.25s ease 0.2s, visibility 0s ease 0.35s,
    -webkit-transform 0.25s ease 0.2s;
  transition: transform 0.25s ease 0.2s, opacity 0.25s ease 0.2s,
    visibility 0s ease 0.35s;
  transition: transform 0.25s ease 0.2s, opacity 0.25s ease 0.2s,
    visibility 0s ease 0.35s, -webkit-transform 0.25s ease 0.2s;
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
  margin-left: 12px;
}

.navigation-main ul ul {
  top: -12px;
  left: 100%;
  margin: 0;
  -webkit-transform: translate(-10px, 0);
  transform: translate(-10px, 0);
}

.navigation-main > li:first-child ul {
  margin-left: 0;
}

.navigation-main li:hover > ul {
  -webkit-transition-delay: 0s, 0s, 0s;
  transition-delay: 0s, 0s, 0s;
  visibility: visible;
  opacity: 1;
}

.navigation-main li:hover ul {
  z-index: 15;
}

.navigation-main > li:hover > ul {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.navigation-main li li:hover > ul {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.navigation-main > .nav-open-left ul {
  right: 0;
}

.navigation-main > .nav-open-left ul ul {
  right: 100%;
  left: auto;
}

.navigation-main li .nav-open-left ul {
  right: 100%;
  left: auto;
}

.navigation-main li .nav-open-left.menu-item-has-children > a::after {
  content: "\f0d9";
  font-size: 12px;
}

.navigation-main li.nav-open-left .menu-item-has-children > a::after {
  content: "\f0d9";
  font-size: 12px;
}

.navigation-main ul {
  padding: 12px 0;
  list-style: none;
  background-color: #fff;
  min-width: 220px;
  border-radius: 3px;
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.08);
  line-height: normal;
}

.navigation-main a {
  position: relative;
  color: #4a4a4a;
  font-size: 16px;
}

.navigation-main > li {
  line-height: 1;
}

.navigation-main > li > ul::before {
  content: "";
  position: absolute;
  top: -12px;
  left: 10px;
  border: 6px solid transparent;
  border-bottom-color: #fff;
  width: 0;
  height: 0;
}

.navigation-main > li[class*="fa-"] {
  font-weight: normal;
  line-height: 1;
}

.navigation-main > li[class*="fa-"]:not(:first-child) {
  margin-left: 10px;
}

.navigation-main > li[class*="fa-"] > a {
  display: inline-block;
  margin-left: 0;
}

.navigation-main > li[class*="fa-"]::before {
  color: #259bea;
  margin-right: 5px;
  font-size: 1.2em;
}

.navigation-main > li.fas::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.navigation-main > li.far::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
}

.navigation-main > li.fab::before {
  font-family: "Font Awesome 6 Brands";
  font-weight: 400;
}

.navigation-main > li > a {
  padding: 15px 0;
  margin: 0 12px;
  color: #333;
}

.navigation-main > li:hover > a,
.navigation-main > li > a:focus,
.navigation-main > .current-menu-item > a,
.navigation-main > .current-menu-parent > a,
.navigation-main > .current-menu-ancestor > a {
  color: #259bea;
}

.navigation-main li li a {
  padding: 6px 15px;
  font-size: 14px;
}

.navigation-main li li:hover > a,
.navigation-main li li > a:focus,
.navigation-main li .current-menu-item > a,
.navigation-main li .current-menu-parent > a,
.navigation-main li .current-menu-ancestor > a {
  color: #259bea;
}

.navigation-main .menu-item-has-children > a::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 8px;
  position: relative;
  top: -2px;
  right: -5px;
  line-height: normal;
  color: #259bea;
}

.navigation-main li .menu-item-has-children > a {
  padding-right: 25px;
}

.navigation-main li .menu-item-has-children > a::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  color: inherit;
}

.navigation-main .nav-button > a {
  border: 0;
  border-radius: 3px;
  padding: 4px 28px 5px;
  margin-left: 10px;
  background-color: #259bea;
  font-size: 15px;
}

.navigation-main .nav-button > a:hover {
  color: #fff;
}

.navigation-main .nav-button > a::before {
  display: none;
}

.navigation-main .nav-button:last-child > a {
  margin-right: 0;
}

.navigation-main .mega-menu > ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.navigation-main .mega-menu > ul > li > a {
  font-weight: 600;
  font-size: 1.05em;
}

.navigation-main .mega-menu > ul > li > a,
.navigation-main .mega-menu > ul > li > a:focus,
.navigation-main .mega-menu > ul > li.current-menu-item > a,
.navigation-main .mega-menu > ul > li.current-menu-parent > a,
.navigation-main .mega-menu > ul > li.current-menu-ancestor > a {
  background-color: transparent;
  color: initial;
}

.navigation-main .mega-menu > ul ul {
  display: block;
  opacity: 1;
  box-shadow: none;
  padding: 0;
  margin: 0;
  position: static;
  background: none;
  min-width: 180px;
  -webkit-transform: translate(0, 10px);
  transform: translate(0, 10px);
}

.navigation-main .mega-menu > ul a::after {
  display: none;
}

.navigation-main .mega-menu:hover ul ul {
  opacity: 1;
  visibility: visible;
  -webkit-transform: none;
  transform: none;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
}

.navigation-main-right {
  text-align: right;
}

.navigation-main-right > li:last-child ul {
  right: 0;
}

.navigation-main-right > li:last-child ul ul {
  left: auto;
  right: 100%;
}

.navigation-main-right > li:last-child .menu-item-has-children > a::after {
  content: "\f0d9";
  font-size: 12px;
}

/* -----------------------------------------
  03. Header
----------------------------------------- */

.top-head-wrap {
  background-color: #259bea;
  color: #fff;
}

.top-head-left {
  float: left;
}
.top-head-right {
  float: right;
}
.top-head-center {
  text-align: center;
  font-size: 16px;
  line-height: 45px;
  max-height: 47px;
  font-weight: bold;
  overflow: hidden;
}
.top-head-wrap .list-social-icons li {
  margin: 0;
}
.top-head-wrap .social-icon {
  border: none;
  width: 45px;
  height: 45px;
}

.top-head-wrap .sub-menu {
  display: none;
}

.navigation-top > li {
  display: inline-block;
  line-height: 0;
}
.navigation-top > li > a {
  display: block;
  margin: 0 8px;
  font-size: 14px;
  color: #fff;
  line-height: 45px;
}
.header {
  position: relative;
  padding: 0;
  z-index: 20;
}

.site-logo {
  text-rendering: optimizeLegibility;
  letter-spacing: -0.01em;
  font-size: 34px;
  margin: 0;
  font-weight: 700;
  line-height: 1;
  z-index: 9999;
}

.site-logo a {
  color: #000;
}

.custom-logo-link img {
  max-width: 190px;
  max-height: 80px;
  width: auto;
}
.site-tagline {
  font-size: 14px;
  margin: 0;
  color: #ea593c;
  line-height: normal;
}

.head-mast {
  background-color: #fff;
  color: #000;
  box-shadow: 0px 1px 11px 0px rgb(0 0 0 / 15%);
}

.head-mast-row {
  position: relative;
  height: 80px;
}
.header-branding-wrap,
.head-nav,
.head-icons {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.head-mast-row {
  align-items: center;
}
.header-branding-wrap {
  position: initial;
}
.head-mast-row  .product-name {
    display: none;
    font-size: 20px;
    margin-left: 10px;
    border-left: 1px solid #e3e3e3;
    padding: 5px 10px;
    font-weight: bold;
}
.head-mast-row  .star-rating{
   display: block;
   margin-top: 8px;
}
.head-mast-row  .product-price {
    display: none;
    margin-right: 0;
    margin-left: auto;
    font-size: 20px;
    font-weight: bold;
    color: #259bea;
}
.head-mast-row  .product-price ins {
    text-decoration: none;
}
.head-mast-row  .product-price del {
      color: #aeaeae;
}
.header-branding-wrap {
  z-index: 9999;
  left: 0;
}
.head-icons {
  right: 0;
}
.head-nav {
  width: 100%;
}
/* Header search form
=================================== */

.category-search-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 40px;
  position: relative;
  background-color: #f7f8f8;
  border-radius: 50px;
}

.category-search-select {
  display: none;
  max-width: 178px;
  height: 100%;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
  color: #666;
  font-size: 14px;
  border-radius: 3px 0 0 3px;
  border: 0;
  border-right: 1px solid #eee;
}

.category-search-input-wrap {
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
  position: relative;
  background: #f7f8f8;
  border-radius: 50px;
}

.category-search-input {
  height: 100%;
  border: 0;
  font-size: 14px;
  border-radius: 50px;
  background: #f7f8f8;
}

.category-search-spinner {
  border: 4px solid rgba(0, 168, 222, 0.35);
  border-top-color: rgba(0, 168, 222, 0.875);
  border-radius: 100%;
  height: 24px;
  width: 24px;
  -webkit-animation: rot 0.8s infinite linear;
  animation: rot 0.8s infinite linear;
  position: absolute;
  right: 10px;
  top: 7px;
  opacity: 0;
  -webkit-transition: opacity 0.18s ease;
  transition: opacity 0.18s ease;
}

.category-search-spinner.visible {
  opacity: 1;
}

.category-search-results {
  position: absolute;
  list-style: none;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 0 0 3px 3px;
  margin: 1px 0 0;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  z-index: 25;
  display: none;
}

.category-search-results-item {
  margin: 0;
}

.category-search-results-item a {
  padding: 7px 15px;
  display: block;
  color: #666;
}

.category-search-results-item a:hover {
  background-color: #f6f9fc;
}

.category-search-results-item.highlighted a {
  background-color: #f6f9fc;
}

.category-search-results-item.error a {
  color: #ff786d;
}

button.category-search-btn {
  width: 40px;
  height: 100%;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  padding: 0;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50px;
  background: #f7f8f8;
}

/* Header search
=================================== */
.head-search-form-wrap {
  display: none;
  padding: 10px 0 20px;
}
.header-search-icon {
  padding: 15px;
  margin-right: 15px;
  background-color: #f7f8f8;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  line-height: 0;
}
.header-search-icon svg {
  fill: #4c4c4c;
  cursor: pointer;
  width: 18px;
  height: 18px;
}
.category-search-input {
  border-radius: 50%;
  padding-left: 25px;
}
button.category-search-btn {
  background-color: #f7f8f8;
  color: #4c4c4c;
  border-radius: 50%;
}
/* Mini Cart
=================================== */

.header-mini-cart {
  position: relative;
  z-index: 25;
  background-color: #f7f8f8;
  border-radius: 50%;
}
.head-mini-cart-wrap {
  padding: 0 15px;
}
.header-mini-cart .widget-title {
  display: none;
}

.header-mini-cart-trigger {
  display: block;
  position: relative;
  padding-left: 14px;
  width: 48px;
  height: 48px;
  line-height: 53px;
}
.header-mini-cart-trigger:focus {
  outline: none;
}
.header-mini-cart-trigger .fas {
  font-size: 18px;
  margin-right: 3px;
  position: relative;
  color: #4c4c4c;
}

.header-mini-cart-contents {
  position: absolute;
  right: 0;
  background-color: #fff;
  padding: 20px;
  border-radius: 3px;
  min-width: 300px;
  box-shadow: 0px 0 20px 10px rgba(0, 0, 0, 0.1);
  margin-top: 15px;
  display: none;
}

.header-mini-cart-contents::before {
  content: "";
  position: absolute;
  top: -16px;
  right: 10px;
  border: 8px solid transparent;
  border-bottom-color: #fff;
  width: 0;
  height: 0;
}
.cart-count {
  display: inline-block;
  color: #fff !important;
  position: absolute;
  top: -2px;
  right: 0;
  background: red;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  line-height: 13px;
  text-align: center;
  font-size: 12px;
  border: 2px solid #fff;
}
/* Sticky header
=================================== */

.head-sticky.is-stuck {
  z-index: 99;
}

.head-mast-container,
.head-search-container {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  padding-right: 15px;
  padding-left: 15px;
}
.head-search-container {
  width: calc(100% - 40px);
}

.head-mast-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* -----------------------------------------
  04. Hero Area
----------------------------------------- */

/* Basic Styles
=================================== */

.page-hero {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 60px 0;
  background-color: #251e36;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  color: #fff;
  height: 300px;
}

.page-hero::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 12;
  background-color: rgba(57, 78, 100, 0.4);
}

.slick-initialized .page-hero {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.page-hero .btn {
  margin-top: 25px;
}

.page-hero-lg {
  overflow: hidden;
  height: 300px;
}

.page-hero-slideshow .page-hero-lg {
  height: 100%;
}

.page-hero-align-center {
  text-align: center;
}

.page-hero-align-left {
  text-align: left;
}

.page-hero-align-right {
  text-align: right;
}

.page-hero-align-top {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.page-hero-align-middle {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.page-hero-align-bottom {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.page-hero-content {
  position: relative;
  z-index: 15;
}

.page-hero-title {
  color: inherit;
  margin: 0;
  font-size: 42px;
  font-weight: 600;
}

.page-hero-subtitle {
  margin: 0;
  font-size: 24px;
  letter-spacing: -0.01em;
  line-height: 1.1;
}

/* Slideshow
=================================== */

.page-hero-slideshow {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.page-hero-slideshow .slick-list,
.page-hero-slideshow .slick-track {
  height: 100%;
}

.page-hero-slideshow .slick-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
}

.napoleon-slick-slider .slick-arrow {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  border: 1px solid #dddddd;
  border-radius: 3px;
  color: #4a4a4a;
  width: 46px;
  height: 46px;
  font-size: 14px;
  padding: 0;
  text-align: center;
  line-height: normal;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 1001;
  display: inline-block;
  background: transparent;
  border: 1px solid #fff;
  color: #fff;
}

.napoleon-slick-slider .slick-arrow:hover {
  background-color: #259bea;
  color: #fff;
  border-color: transparent;
}

.napoleon-slick-slider .slick-arrow.slick-disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.napoleon-slick-slider .slick-arrow:hover {
  color: #fff;
  background: #259bea;
  border-color: #259bea;
}

.napoleon-slick-slider .slick-next {
  margin-left: 10px;
}

.napoleon-slick-slider .slick-next .fa {
  position: relative;
  left: 2px;
}

.napoleon-slick-slider .slick-prev {
  left: 15px;
}

.napoleon-slick-slider .slick-prev .fa {
  position: relative;
  right: 1px;
}

.napoleon-slick-slider .slick-dots {
  position: absolute;
  margin: 0;
  padding: 0;
  list-style-type: none;
  text-align: center;
  width: 100%;
  bottom: 10px;
}

.napoleon-slick-slider .slick-dots li {
  display: inline-block;
  margin: 0 5px;
}

.napoleon-slick-slider .slick-dots button {
  position: relative;
  text-indent: -999em;
  padding: 0;
  margin: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  border: 0;
  box-shadow: none;
  cursor: pointer;
  opacity: 0.5;
}

.napoleon-slick-slider .slick-dots .slick-active button {
  opacity: 1;
}

.page-hero-slideshow-nav {
  position: absolute;
  right: 15px;
  bottom: 75px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

/* -----------------------------------------
  05. Modules
----------------------------------------- */

/* General Layout
=================================== */

.main {
  padding: 70px 0;
}

/* Sections
=================================== */

.widget-section {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  margin-bottom: 70px;
}

.widget-section:last-child {
  margin-bottom: 0;
}

.widget-section-parallax {
  background-attachment: fixed;
}

.section-padded {
  padding-top: 70px;
  position: relative;
}

.section-heading {
  margin-bottom: 30px;
  position: relative;
  text-align: center;
}
.woocommerce-order-received  .section-heading {
  margin-top: 50px;
  margin-bottom: 0;
}
.section-heading-content .term-description {
  margin-top: 15px;
}

.section-title {
  display: inline-block;
  margin-bottom: 10px;
  color: #000;
  position: relative;
  text-align: center;
  font-size: 26px;
}

.section-title:after {
  content: "";
  display: block;
  width: 100%;
  height: 6px;
  position: absolute;
  background: #259bea;
  opacity: 0.6;
  bottom: -2px;
  left: 0;
  right: 0;
  z-index: -1;
}

.section-title > a {
  color: #ea593c;
  font-size: 16px;
  margin-left: 5px;
}

.section-subtitle {
  text-align: center;
  margin: 0;
}

.section-subtitle a {
  color: #4a4a4a;
}

.widget_at-home-latest-products .row-items[class*="columns"],
.row-items[class*="columns"],
.thanks-products {
  overflow: hidden;
  display: grid;
  grid-gap: 25px;
  padding: 15px;
}

.widget_at-home-latest-products .row-items.columns-2,
.row-items.columns-2 {
  grid-template-columns: repeat(2, 1fr);
}
.widget_at-home-latest-products .row-items.columns-3,
.row-items.columns-3 {
  grid-template-columns: repeat(3, 1fr);
}
.widget_at-home-latest-products .row-items.columns-4,
.row-items.columns-4 {
  grid-template-columns: repeat(4, 1fr);
}
.widget_at-home-latest-products .row-items.columns-5,
.row-items.columns-5,
.thanks-products {
  grid-template-columns: repeat(5, 1fr);
}

.widget_at-home-latest-products .row-items.slick-slider {
  display: block;
}
.widget_at-home-latest-products .row-items.slick-slider [class*="col-"] {
  max-width: unset;
  margin: 15px;
}
.widget_at-home-latest-products
  .row-items.slick-slider
  [class*="col-"]
  .item-product {
  margin-bottom: 15px;
}
.widget_at-home-latest-products .row-items [class*="col-"],
.row-items [class*="col-"],
.thanks-products [class*="col-"] {
  position: relative;
  flex: none;
  max-width: 100%;
  border: 1px solid #efefef;
  border-radius: 3px;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.15);
  padding: 0;
  padding-bottom: 50px;
  background-color: #fff;
}

.thanks-products [class*="col-"],
.thanks-products  .item-product {
  padding-bottom: 0;
}
.widget_at-home-latest-posts .row-items [class*="col-"],
.widget_at-home-post-type-items .row-items [class*="col-"] {
  padding-bottom: 0;
}
.no-buy-button .widget_at-home-latest-products .row-items [class*="col-"] {
  padding-bottom: 0;
}
.no-shadow-effect .widget_at-home-latest-products .row-items [class*="col-"] {
  box-shadow: none;
  border: none;
}

.no-shadow-effect
  .widget_at-home-latest-products
  .row-items
  [class*="col-"]
  .add_to_cart_button {
  width: 100%;
}
.widget_at-home-latest-products .item {
  border: 0;
  padding-bottom: 0;
}
.widget_at-home-latest-products .item-product .button,
.row-items [class*="col-"] .item-product .button {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  width: calc(100% - 40px);
  padding: 13px 0;
  transition: all 0.25s;
  background-image: url(assets/img/button-bg.svg);
}
#nrwooconfirm,
#nrwooconfirm.atc-buy-button .button, 
#nrwooconfirm.atc-buy-button input {
  background-image: url(assets/img/button-bg.svg);
}

/* Entry Styles
=================================== */

.entry {
  margin: 0 0 100px;
}

.entry:only-of-type {
  margin-bottom: 0;
}

.entry-header {
  margin-bottom: 25px;
}

.entry-title {
  font-size: 28px;
  margin: 2px 0 0;
}

.entry-title a {
  color: #4a4a4a;
}

.entry-title a:hover {
  color: #259bea;
}

.entry-meta {
  font-size: 16px;
  color: #8e8e8e;
}

.item-media .entry-meta {
  margin-bottom: 15px;
}

.entry-meta a {
  color: #666;
}

.entry-meta span::after {
  content: "\2022";
  margin: 0 4px;
  opacity: 0.5;
}

.entry-meta span:last-child::after {
  display: none;
}

.entry-meta-item.entry-sticky::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 0.7em;
  position: relative;
  top: -1px;
}

.entry-thumb {
  margin-bottom: 50px;
}

.entry-thumb img {
  border-radius: 3px;
  border: 1px solid #dddddd;
}

.entry-thumb.alignnone,
.entry-thumb.alignleft,
.entry-thumb.alignright,
.entry-thumb.aligncenter {
  margin-top: 0;
}

.entry-content::after {
  content: "";
  display: table;
  clear: both;
}

.entry-content h1,
.entry-content h2,
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6 {
  margin: 35px 0 30px;
}

.entry-content h1:first-child,
.entry-content h2:first-child,
.entry-content h3:first-child,
.entry-content h4:first-child,
.entry-content h5:first-child,
.entry-content h6:first-child {
  margin-top: 0;
}

.entry-content .fluid-width-video-wrapper,
.entry-content audio,
.entry-content video,
.entry-content blockquote {
  margin: 40px 0;
}

.entry-content .fluid-width-video-wrapper:first-child,
.entry-content audio:first-child,
.entry-content video:first-child,
.entry-content blockquote:first-child {
  margin-top: 0;
}

.entry-content .fluid-width-video-wrapper:last-child,
.entry-content audio:last-child,
.entry-content video:last-child,
.entry-content blockquote:last-child {
  margin-bottom: 0;
}

.entry-content blockquote {
  font-size: 20px;
  position: relative;
  padding-left: 35px;
  border: 0;
  line-height: 1.428571429;
}

.entry-content blockquote::before {
  content: "\00201C";
  font-size: 4.3em;
  line-height: 0;
  position: absolute;
  left: -10px;
  top: 34px;
  opacity: 0.35;
}

.entry-content-intro {
  font-size: 20px;
  line-height: 1.4;
  margin-bottom: 25px;
}

.entry-tags {
  margin: 30px 0 0;
}

.entry-tags a {
  text-transform: capitalize;
  color: #666;
}

.entry-more-btn {
  margin-top: 25px;
}

.entry-author-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 65px 0 0;
}

.section-related {
  margin-top: 65px;
}

.entry-author-desc {
  border-radius: 3px;
  background-color: #f6f9fc;
  padding: 25px 30px;
}

.entry-author-thumbnail {
  width: 80px;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  margin-right: 30px;
}

.entry-author-thumbnail img {
  border-radius: 3px;
}

.entry-author-title {
  font-size: 18px;
  margin-bottom: 15px;
}

.entry-author-subtitle {
  margin-top: -15px;
  font-size: 14px;
}

.post-password-form {
  padding: 20px;
  background-color: #f6f9fc;
  border-radius: 3px;
}

/* Items & Item Listings
=================================== */

.row-items {
  margin-bottom: -30px;
}

.row-items.no-gutters {
  margin-bottom: 0;
}

.item {
  margin-bottom: 20px;
}

.row-items [class*="col-"]:hover {
  border-color: #259bea;
}
.row-items [class*="col-"]:hover .item-thumb img {
  opacity: 0.9;
}
.row-items [class*="col-"]:hover .item-product .button {
  background-color: #ea593c;
}
.no-gutters .item,
.slick-slide .item {
  margin-bottom: 0;
}

.item-media {
  border: 0;
  border-radius: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 70px;
}

.item-media.item-media-sm {
  margin-bottom: 15px;
}

.item-thumb img {
  aspect-ratio: 1 / 1;
  object-fit: cover;
  width: 100%;
  transition: all 0.25s;
}

.item-media .item-thumb {
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  width: 300px;
  margin-right: 30px;
}

.item-media .item-thumb img {
  border-radius: 3px;
}

.item-media .item-thumb img {
  border: 1px solid #dddddd;
}

.item-media-sm .item-thumb {
  width: 70px;
  margin-right: 15px;
}

.item-content {
  text-align: center;
  min-width: 0;
  padding: 10px;
  background-color: #fff;
}

.item-media .item-content {
  padding: 0;
}

.item-meta {
  font-size: 13px;
  line-height: normal;
  margin-bottom: 3px;
  color: #8e8e8e;
  letter-spacing: -0.01em;
}

.item-meta a {
  color: #8e8e8e;
}

.item-media .item-meta {
  margin-bottom: 5px;
  font-size: 16px;
}

.item-media-sm .item-meta {
  font-size: 13px;
}

.item-title {
  color: #000;
  line-height: normal;
  font-size: 16px;
  letter-spacing: -0.01em;
  margin-bottom: 5px;
}

.item-title a {
  color: #000;
}

.item-title a:hover {
  color: #259bea;
}

.item-media-sm .item-title {
  font-size: 14px;
}

.item .price {
  display: block;
  font-size: 16px;
  color: #259bea;
  margin-bottom: 5px;
  font-weight: 600;
}

.item .price del {
  opacity: 0.5;
  font-size: 15px;
  margin-right: 3px;
  font-weight: 400;
  color: #747474;
}

.item .price:last-child {
  margin-bottom: 0;
}

.item-inset {
  color: #259bea;
  margin-bottom: 0;
  line-height: normal;
}

.item-inset a {
  color: currentColor;
}

.item-read-more {
  margin-top: 20px;
}

/* Block Items
=================================== */

.block-layout {
  margin-bottom: -30px;
}

.block-item {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 3px;
  margin-bottom: 30px;
  position: relative;
  height: 0;
  overflow: hidden;
}

.block-item::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgb(43 44 44 / 40%);
}

.block-item-xl,
.block-item-lg {
  padding-bottom: 56.25%;
}

.block-item-md {
  padding-bottom: 54.69462%;
}

.block-item-long {
  height: 215px;
}

.block-item-content-wrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.block-item-content {
  padding: 30px;
  color: #fff;
}

.block-item-title {
  font-size: 26px;
  margin: 0;
  line-height: normal;
  letter-spacing: -0.01em;
  font-weight: 600;
}

.block-item-subtitle {
  font-size: 16px;
  margin: 0;
  line-height: normal;
  letter-spacing: -0.01em;
}

.btn-block-item {
  margin-top: 15px;
}

/* Pagination
=================================== */

.woocommerce-pagination {
  margin: 30px 0 0;
  background-color: #f6f9fc;
  border-radius: 3px;
  padding: 10px;
  text-align: center;
}

.woocommerce-pagination ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.woocommerce-pagination li {
  display: inline-block;
  list-style: none;
}

.woocommerce-pagination a,
.woocommerce-pagination span {
  display: inline-block;
  margin: 2px 6px;
  color: #666;
}

.woocommerce-pagination a:hover,
.woocommerce-pagination .current {
  color: #259bea;
}

.navigation {
  margin: 30px 0 0;
  background-color: #f6f9fc;
  border-radius: 3px;
  padding: 10px;
  text-align: center;
}

.navigation a,
.navigation .page-numbers {
  display: inline-block;
  margin: 2px 6px;
  color: #666;
}

.navigation a:hover,
.navigation .current {
  color: #259bea;
}

.navigation .nav-links::after {
  content: "";
  display: table;
  clear: both;
}

.navigation .nav-previous {
  float: left;
}

.navigation .nav-next {
  float: right;
}

.comment-navigation {
  margin: 30px 0;
}

.page-links {
  margin: 30px 0 0;
  background-color: #f6f9fc;
  border-radius: 3px;
  padding: 10px;
  text-align: center;
}

.page-links .page-number {
  display: inline-block;
  margin: 2px 6px;
  color: #666;
}

.page-links .page-number:hover {
  color: #259bea;
}

.page-links > .page-number {
  color: #666;
}

.page-links > a:first-child,
.page-links > span:first-child {
  margin-left: 10px;
}

/* Social Icons
=================================== */

.list-social-icons,
.list-card-icons {
  margin: 0;
  padding: 0;
  list-style: none;
}

.list-social-icons li,
.list-card-icons li {
  display: inline-block;
  margin: 3px;
}

.social-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  color: #666;
  border: 2px solid #dddddd;
  border-radius: 50%;
}

.social-icon:hover {
  border-color: #259bea;
  color: #259bea;
}

.header .social-icon {
  color: inherit;
  font-size: inherit;
}

.entry-author-socials .social-icon {
  border: 0;
  padding: 0;
  width: auto;
  height: auto;
  font-size: 18px;
  color: #ea593c;
}

.entry-social-share {
  text-align: center;
}

.entry-social-share-title {
  margin-bottom: 10px;
}

.callus-icon {
  width: 50px;
  height: 50px;
  display: block;
  text-align: center;
  background-color: #259bea;
  color: #fff;
  position: fixed;
  bottom: 81px;
  right: 15px;
  border-radius: 50%;
  z-index: 99999;
  box-shadow: -1px 4px 7px rgb(0 0 0 / 25%);
}
.callus-icon i {
  font-size: 26px;
  line-height: 50px;
  animation: rotate-tel 8s ease infinite;
}

.callus-icon:hover {
  color: #fff;
}

@keyframes rotate-tel {
  0%,
  100% {
    transform: translateZ(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: rotate(-5deg);
  }
  20%,
  40%,
  60%,
  80% {
    transform: rotate(30deg);
  }
}
/* Item sliders
=================================== */

.row-items.slick-initialized {
  margin-bottom: 0;
}

.row-items.slick-initialized [class^="col"] {
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
  max-width: 100%;
}

.row-slider-nav {
  top: 10px;
  margin-left: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 25px;
  width: auto;
}

.row-slider-nav .slick-arrow {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  border: 1px solid #dddddd;
  border-radius: 3px;
  color: #4a4a4a;
  width: 46px;
  height: 46px;
  font-size: 14px;
  padding: 0;
  text-align: center;
  line-height: normal;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: transparent;
  border-radius: 50%;
}

.row-slider-nav .slick-arrow:hover {
  background-color: #259bea;
  color: #fff;
  border-color: transparent;
}

.row-slider-nav .slick-arrow.slick-disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.row-slider-nav .slick-arrow:hover {
  opacity: 1;
}

.row-slider-nav .slick-next {
  margin-left: 10px;
}
.row-slider-nav .slick-next {
  position: absolute;
  right: 0;
}
.row-slider-nav .slick-prev {
  position: absolute;
  left: 0;
}

/* -----------------------------------------
  06. Footer
----------------------------------------- */

.footer,
.footer a,
.footer-widgets .widget_nav_menu li a {
  color: #333;
}

.footer-widgets {
  background-color: #fafafa;
  padding: 70px 0 10px;
  font-size: 14px;
  line-height: 1.65;
}

.footer-info {
  padding: 15px 0;
  font-size: 14px;
  background-color: #efefef;
}

.footer-credit,
.footer-credit a {
  color: #333;
}

.footer-credit {
  margin-bottom: 0;
  text-align: right;
}
.footer-widgets .widget-title {
  font-weight: bold;
}
.footer-widgets .widget-title:after {
  content: "";
  display: block;
  width: 25px;
  height: 1px;
  background-color: #259bea;
  margin: 8px 0 0;
}

.footer-widgets .widget a:hover,
.footer-widgets .item-title a:hover {
  color: #259bea;
}
.footer-widgets .at-schedule-widget-table tr,
.footer-widgets .widget_meta li a,
.footer-widgets .widget_pages li a,
.footer-widgets .widget_categories li a,
.footer-widgets .widget_archive li a,
.footer-widgets .widget_nav_menu li a,
.footer-widgets .widget_product_categories li a,
.footer-widgets .widget_layered_nav li a,
.footer-widgets .widget_rating_filter li a,
.footer-widgets .widget_recent_entries li,
.footer-widgets .widget_recent_comments li,
.footer-widgets .widget_rss li,
.footer-widgets .tag-cloud-link {
  border-bottom: none;
}
/* -----------------------------------------
  07. Comments
----------------------------------------- */

.comments-area {
  margin: 65px 0 0;
}

.comments-title,
.comment-reply-title {
  display: block;
  margin-bottom: 30px;
  font-size: 26px;
  text-align: center;
  font-weight: bold;
}

.comment-list,
.commentlist {
  margin: 0;
  list-style: none;
  padding: 0;
}

.comment-list .children,
.commentlist .children {
  list-style: none;
}

.comment-list .comment-body,
.commentlist .comment-body {
  margin-bottom: 20px;
  padding-top: 20px;
}

.comment-list > .comment:first-child > .comment-body,
.commentlist > .comment:first-child > .comment-body {
  border-top: 0;
  padding-top: 0;
}

.post-comments {
  margin: 0 0 45px;
}

.comment-author .fn {
  font-size: 18px;
  font-weight: 400;
}

.comment-author .avatar {
  width: 80px;
  height: 80px;
  float: left;
  margin: 0 15px 15px 0;
  overflow: visible;
  border-radius: 3px;
}

.comment-content {
  overflow: hidden;
  zoom: 1;
}

.comment-content ul,
.comment-content ol {
  margin: 30px 0;
  padding-left: 25px;
}

.comment-content ul ul,
.comment-content ul ol,
.comment-content ol ul,
.comment-content ol ol {
  margin: 0;
}

.comment-content blockquote {
  margin: 35px 0 0;
}

.comment-metadata {
  font-size: 14px;
  margin: 0 0 5px;
}

.comment-metadata a {
  color: #8e8e8e;
}

.comment-reply-link {
  font-size: 10px;
  text-transform: uppercase;
  padding: 4px 10px;
  margin-left: 94px;
  height: auto;
  width: auto;
}

.bypostauthor > article .fn::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  margin: 0 2px 0 -2px;
  position: relative;
  top: -1px;
  font-size: 11px;
}

.comment-respond {
  margin-top: 50px;
}

#cancel-comment-reply-link {
  font-size: 13px;
  font-weight: normal;
  margin-left: 5px;
}

.form-allowed-tags,
.comment-notes {
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.75;
}

.form-submit {
  margin-bottom: 0;
}

.no-comments {
  border: 1px solid #dddddd;
  padding: 15px;
  margin-top: 40px;
  text-align: center;
}

.comment-form-cookies-consent {
  line-height: normal;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.comment-form-cookies-consent label {
  display: inline-block;
  margin: 0 0 0 8px;
}

.comment-form > p {
  margin: 0 0 20px;
}

.comment-form .form-submit {
  margin-top: 30px;
}
.comment-form .form-submit input[type="submit"] {
  width: 200px;
}

/* -----------------------------------------
  08. Widgets Styling
----------------------------------------- */

.sidebar-drawer {
  position: fixed;
  top: 0;
  left: -410px;
  background-color: #fff;
  height: 100%;
  width: 340px;
  box-shadow: 4px 0 7px rgba(0, 0, 0, 0.05);
  -webkit-transition: left 0.25s ease;
  transition: left 0.25s ease;
}

.admin-bar .sidebar-drawer {
  top: 33px;
}

.sidebar-drawer-header {
  background-color: #f6f9fc;
  text-align: right;
  height: 40px;
  padding: 8px 15px;
}

.sidebar-dismiss {
  color: #666;
  font-size: 20px;
  line-height: 1;
}

.sidebar-dismiss:hover {
  color: #666;
}

.sidebar-drawer-content {
  padding: 25px;
  height: calc(100% - 45px);
  overflow-y: auto;
}

.sidebar-drawer-visible {
  left: 0;
  z-index: 150;
}

.widget {
  margin: 0 0 60px;
  /* Nullify bottom margin for last elements in widgets and sidebars */
}

.sidebar .widget:last-child {
  margin-bottom: 0;
}

.widget p:last-child {
  margin-bottom: 0;
}

.widget select {
  width: 100%;
  height: 30px;
}

.widget-title {
  font-size: 18px;
  margin-bottom: 15px;
}

.widget-title label {
  text-transform: none;
  display: block;
  font-size: inherit;
  margin: 0;
  line-height: inherit;
  font-weight: inherit;
}

/* WIDGET: #Widget - Home Newsletter
========================================= */

.widget_at-home-newsletter {
  padding: 0;
  background-color: #259bea;
}

.widget-newsletter-wrap {
  padding: 30px 0;
}

.widget-newsletter-content-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.widget-newsletter-content-wrap .fas,
.widget-newsletter-content-wrap .far {
  color: #259bea;
  font-size: 40px;
  line-height: 70px;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  margin-right: 15px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: white;
  text-align: center;
}

.widget-newsletter-content {
  line-height: normal;
  color: #ea593c;
}

.widget-newsletter-content {
  color: rgb(255 255 255 / 50%) !important;
}
.widget-newsletter-content h2 {
  color: #fff;
  margin-bottom: 0;
  font-weight: 900;
}

.widget-newsletter-content p {
  margin-bottom: 0;
}

.widget-newsletter-form {
  position: relative;
  height: 40px;
}

.widget-newsletter-form button {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  padding: 0 25px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.widget-newsletter-form input[type="email"] {
  height: 100%;
}

/* WIDGET: Theme - Contact Widget
========================================= */

.at-contact-widget-title {
  font-weight: 600;
  font-size: 16px;
  margin: 0 0 15px;
}

.at-contact-widget-items {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.at-contact-widget-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  margin-bottom: 5px;
}

.at-contact-widget-item i {
  text-align: center;
  min-width: 26px;
  margin-right: 5px;
}

.footer-widgets .at-contact-widget-item i {
  opacity: 0.65;
}

/* WIDGET: Theme - Opening Hours
========================================= */

.at-schedule-widget-intro {
  margin-bottom: 20px;
}

.at-schedule-widget-table {
  font-size: 16px;
  text-align: left;
}

.at-schedule-widget-table th,
.at-schedule-widget-table td {
  padding: 7px 0;
}

.at-schedule-widget-table tr {
  border-bottom: 1px solid #dddddd;
}

.footer-widgets .at-schedule-widget-table {
  font-size: 14px;
}

/* WIDGET: #List Widgets
========================================= */

.widget_meta ul,
.widget_pages ul,
.widget_categories ul,
.widget_archive ul,
.widget_nav_menu ul,
.widget_product_categories ul,
.widget_layered_nav ul,
.widget_rating_filter ul,
.wp-block-woocommerce-product-categories ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.widget_meta ul ul,
.widget_pages ul ul,
.widget_categories ul ul,
.widget_archive ul ul,
.widget_nav_menu ul ul,
.widget_product_categories ul ul,
.widget_layered_nav ul ul,
.widget_rating_filter ul ul,
.wp-block-woocommerce-product-categories ul ul {
  margin-left: 15px;
}

.widget_meta li,
.widget_pages li,
.widget_categories li,
.widget_archive li,
.widget_nav_menu li,
.widget_product_categories li,
.widget_layered_nav li,
.widget_rating_filter li,
.wp-block-woocommerce-product-categories li {
  line-height: normal;
  display: block;
  position: relative;
}

.widget_meta li a,
.widget_pages li a,
.widget_categories li a,
.widget_archive li a,
.widget_nav_menu li a,
.widget_product_categories li a,
.widget_layered_nav li a,
.widget_rating_filter li a,
.wp-block-woocommerce-product-categories li a {
  display: block;
  padding: 2px 0;
  color: #666;
  border-bottom: 1px solid #dddddd;
  letter-spacing: -0.01em;
}

.widget_meta li > span,
.widget_meta li > .count,
.widget_meta li > .at-count,
.widget_pages li > span,
.widget_pages li > .count,
.widget_pages li > .at-count,
.widget_categories li > span,
.widget_categories li > .count,
.widget_categories li > .at-count,
.widget_archive li > span,
.widget_archive li > .count,
.widget_archive li > .at-count,
.widget_nav_menu li > span,
.widget_nav_menu li > .count,
.widget_nav_menu li > .at-count,
.widget_product_categories li > span,
.widget_product_categories li > .count,
.widget_product_categories li > .at-count,
.widget_layered_nav li > span,
.widget_layered_nav li > .count,
.widget_layered_nav li > .at-count,
.widget_rating_filter li > span,
.widget_rating_filter li > .count,
.widget_rating_filter li > .at-count,
.wp-block-woocommerce-product-categories li > span,
.wp-block-woocommerce-product-categories li > .count,
.wp-block-woocommerce-product-categories li > .at-count {
  position: absolute;
  right: 0;
  top: 6px;
  font-size: 14px;
  padding: 4px 3px;
}

.widget_recent_entries ul,
.widget_recent_comments ul,
.widget_rss ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.widget_recent_entries li,
.widget_recent_comments li,
.widget_rss li {
  display: block;
  padding: 11px 0;
  border-bottom: 1px solid #dddddd;
}

.footer-widgets .widget_recent_entries li,
.footer-widgets .widget_recent_comments li,
.footer-widgets .widget_rss li {
  border-color: rgba(255, 255, 255, 0.1);
}

.widget_recent_entries a,
.widget_recent_comments a,
.widget_rss a {
  color: #666;
}

.footer-widgets .widget_recent_entries a,
.footer-widgets .widget_recent_comments a,
.footer-widgets .widget_rss a {
  color: #fff;
}

.widget_recent_entries a:hover,
.widget_recent_comments a:hover,
.widget_rss a:hover {
  color: #259bea;
}

.widget_recent_entries .post-date {
  display: block;
  font-size: 14px;
  color: #8e8e8e;
}

.footer-widgets .widget_recent_entries .post-date {
  color: rgba(255, 255, 255, 0.7);
}

.rss-date {
  display: block;
  font-size: 14px;
  margin: 3px 0;
}

.tag-cloud-link {
  display: inline-block;
  padding: 4px 14px;
  font-size: 13px !important;
  border-radius: 3px;
  border: 1px solid #4a4a4a;
  color: #4a4a4a;
  margin-bottom: 3px;
}

.tag-cloud-link:hover {
  border-color: #259bea;
  background-color: #259bea;
  color: #fff;
}

.footer-widgets .tag-cloud-link {
  border-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* WIDGET: #Menu
========================================= */

.widget_nav_menu li[class*="fa-"] {
  font-weight: normal;
  line-height: 1;
  position: relative;
}

.widget_nav_menu li[class*="fa-"]:not(:first-child) {
  margin-left: 10px;
}

.widget_nav_menu li[class*="fa-"] > a {
  margin-left: 0;
  padding-left: 20px;
}

.widget_nav_menu li[class*="fa-"]::before {
  position: absolute;
  margin-right: 5px;
  margin-top: -2px;
  font-size: 0.85em;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.widget_nav_menu li.fas::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.widget_nav_menu li.far::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
}

.widget_nav_menu li.fab::before {
  font-family: "Font Awesome 6 Brands";
  font-weight: 400;
}

/* WIDGET: #Search
========================================= */

.searchform > div {
  position: relative;
}

.searchform input[type="text"],
.searchform input[type="search"] {
  height: 40px;
}

.searchform .searchsubmit {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  padding: 0 15px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* WIDGET: #Text Widget
========================================= */

.widget_text p:last-child {
  margin-bottom: 0;
}

/* WIDGET: #Calendar
================================================== */

#wp-calendar {
  width: 100%;
}

#wp-calendar a {
  font-weight: bold;
  font-style: italic;
}

#wp-calendar caption {
  text-align: left;
  margin-top: 10px;
  background: none repeat scroll 0 0 rgba(0, 0, 0, 0.03);
  padding: 9px;
}

#wp-calendar thead {
  font-size: 10px;
}

#wp-calendar thead th {
  background: rgba(0, 0, 0, 0.1);
  font-weight: bold;
  padding: 8px;
}

#wp-calendar tbody td {
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 3px;
}

#wp-calendar tbody td:hover {
  background: rgba(0, 0, 0, 0.1);
}

#wp-calendar tbody .pad {
  background: none;
}

#wp-calendar tfoot #next {
  font-size: 10px;
  text-transform: uppercase;
  text-align: right;
}

#wp-calendar tfoot #prev {
  font-size: 10px;
  text-transform: uppercase;
  padding-top: 10px;
}

/* -----------------------------------------
  09. WordPress Defaults
----------------------------------------- */
/* WordPress Galleries
=================================== */

.gallery {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
  margin: 40px 0;
}

.gallery:first-child {
  margin-top: 0;
}

.gallery:last-child {
  margin-bottom: 0;
}

.gallery-item {
  margin-bottom: 10px;
}

.gallery-item img {
  border-radius: 3px;
  width: 100%;
  max-width: 100%;
}

.gallery-item:hover .gallery-caption {
  opacity: 1;
}

.gallery-columns-1 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.gallery-columns-2 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.gallery-columns-3 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 33.33333%;
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

.gallery-columns-4 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.gallery-columns-5 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 20%;
  flex: 0 0 20%;
  max-width: 20%;
}

.gallery-columns-6 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 16.66667%;
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

.gallery-columns-7 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 14.28571%;
  flex: 0 0 14.28571%;
  max-width: 14.28571%;
}

.gallery-columns-8 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 12.5%;
  flex: 0 0 12.5%;
  max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 11.11111%;
  flex: 0 0 11.11111%;
  max-width: 11.11111%;
}

.gallery-caption {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 12px;
  line-height: 1.5;
  margin: 0 0 0 5px;
  max-height: 50%;
  opacity: 0;
  padding: 6px 8px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: left;
  width: calc(100% - 10px);
  -webkit-transition: opacity 0.18s ease;
  transition: opacity 0.18s ease;
}

.gallery-caption::before {
  content: "";
  height: 100%;
  min-height: 49px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.gallery-columns-6 .gallery-caption,
.gallery-columns-7 .gallery-caption,
.gallery-columns-8 .gallery-caption,
.gallery-columns-9 .gallery-caption {
  display: none;
}



/* WordPress Classes
=================================== */

/* Alignment */

.alignnone {
  margin: 5px 0 20px;
}

p .alignnone {
  margin-bottom: 0;
}

.aligncenter {
  display: block;
  margin: 7px auto;
}

.alignright {
  float: right;
  margin: 7px 0 7px 24px;
}

.alignleft {
  float: left;
  margin: 7px 24px 7px 0;
}

/* Captions */

.wp-caption {
  max-width: 100%;
  margin-bottom: 15px;
}

.wp-caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  padding: 0;
  width: auto;
}

.wp-caption .wp-caption-text {
  font-size: 12px;
  line-height: 17px;
  margin: 3px 0 5px;
  padding: 5px 0 0;
  text-align: left;
  font-style: italic;
}

.sticky {
  /* Provide sticky styles if necessary */
}

/* -----------------------------------------
  09b. Block Styling
----------------------------------------- */

/* Block: Pull Quote
========================================= */

.wp-block-pullquote {
  padding: 35px 0;
  border-top: 2px solid #dddddd;
  border-bottom: 2px solid #dddddd;
}

.wp-block-pullquote blockquote::before {
  display: none;
}

/* -----------------------------------------
  10. Mobile menu
----------------------------------------- */

  #mobilemenu li.menu-item-has-children {
    position: relative;
  }
 #mobilemenu li.menu-item-has-children > a:after {
    content: "";
    display: block;
    border: 5px solid transparent;
    border-top-color: #777;
    position: absolute;
    top: 20px;
    right: 20px;
}
 #mobilemenu li.menu-item-has-children > a.active:after{
    border: 5px solid transparent;
    border-bottom-color: #777;
    top: 15px;
}

#mobilemenu {
    background: #fff;
    overflow: hidden;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    -webkit-transform: translate3d(-370px,0,0);
    -moz-transform: translate3d(-370px,0,0);
    -ms-transform: translate3d(-370px,0,0);
    -o-transform: translate3d(-370px,0,0);
    transform: translate3d(-370px,0,0);
    width: 370px;
    height: 100%;
    z-index: 999999;
    -webkit-transition: -webkit-transform 0.25s ease;
    -moz-transition: -moz-transform 0.25s ease;
    -ms-transition: -ms-transform 0.25s ease;
    -o-transition: -o-transform 0.25s ease;
    transition: transform 0.25s ease;
    overflow-y: auto; /* Enable vertical scroll */

}
#mobilemenu .head-search-form-wrap {
  display: block;
  margin-top: 50px;
  padding: 0;
}
#mobilemenu .head-search-container {
    width: 100%;
}
#mobilemenu.open {
    -webkit-transform: translate3d(0,0,0) !important;
    -moz-transform: translate3d(0,0,0) !important;
    -ms-transform: translate3d(0,0,0) !important;
    -o-transform: translate3d(0,0,0) !important;
    transform: translate3d(0,0,0) !important;
}
.fly-menu-fade {
    background: #222;
    cursor: pointer;
    opacity: 0;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
}
.fly-menu-fade.active {
    opacity: .6;
    height: 100%;
}
.close-btn {
    height: 25px;
    width: 25px;
    background: #acacac;
    border-radius: 50px;
    cursor: pointer;
    position: absolute;
    text-align: center;
    display: block;
    padding-top: 10px;
    padding-left: 5px;
    right: 16px;
    top: 15px;
}
.close-btn .bar {
    width: 15px;
    height: 2px;
    background: #fff;
    position: absolute;
}
.close-btn .bar:nth-child(1) {
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    transform: rotate(45deg);
}
.close-btn .bar:nth-child(2) {
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.mobile-nav-trigger {
  text-transform: uppercase;
  display: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: inherit;
  margin-right: 10px;
  background-color: #f7f8f8;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  color: #4c4c4c;
}

.mobile-nav-trigger:hover {
  color: inherit;
}

/* -----------------------------------------
  11. E-Commerce (WooCommerce)
----------------------------------------- */

/* General
========================================= */

.demo_store {
  position: fixed;
  bottom: 0;
  z-index: 150;
  width: 100%;
  background-color: #f6f9fc;
  border-top: 1px solid #dddddd;
  text-align: center;
  margin: 0;
  line-height: normal;
  padding: 15px 25px;
}

.woocommerce-store-notice__dismiss-link {
  display: inline;
}

.entry-content .woocommerce::after {
  content: "";
  display: table;
  clear: both;
}

.woocommerce-message,
.woocommerce-error,
.woocommerce-info,
.woocommerce-noreviews {
  padding: 14px 20px 14px 55px;
  background-color: #f6f9fc;
  margin-bottom: 30px;
  margin-left: 0;
  clear: both;
  border-left: 0.618em solid #a4ecd3;
  position: relative;
}

.woocommerce-message::before,
.woocommerce-error::before,
.woocommerce-info::before,
.woocommerce-noreviews::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f058";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  left: 15px;
  top: 10px;
  font-size: 1.4em;
}

.woocommerce-message a:not(.button),
.woocommerce-error a:not(.button),
.woocommerce-info a:not(.button),
.woocommerce-noreviews a:not(.button) {
  border-bottom: 1px solid;
  color: #666;
}

.woocommerce-message .button,
.woocommerce-error .button,
.woocommerce-info .button,
.woocommerce-noreviews .button {
  float: right;
  padding: 5px 10px;
  line-height: normal;
  font-style: normal;
  font-weight: normal;
  color: inherit;
  background: none;
  position: relative;
  text-decoration: none;
  text-align: left;
  text-transform: uppercase;
  font-size: 0.875em;
  border: 0;
}
.woocommerce-message .button:hover {
  color: #fff;
}

.woocommerce-message .button::after,
.woocommerce-error .button::after,
.woocommerce-info .button::after,
.woocommerce-noreviews .button::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f061";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 0.65em;
  display: inline-block;
  margin: 0 0 0 5px;
  position: relative;
  top: -2px;
}

.woocommerce-message .button.wc-forward {
    background: black;
    color: #fff;
    border-radius: 15px;
    font-weight: bold;
}

a.button.wc-forward:hover {
    padding-left: 15px;
    padding-right: 15px;
}
.woocommerce-error {
  list-style: none;
  border-left-color: #ff786d;
}

.woocommerce-error li {
  display: block;
}

.woocommerce-info {
  list-style: none;
  border-left-color: #54b5f7;
}

.woocommerce-info li {
  display: block;
}

.woocommerce-info:before {
  content: "\f05a";
}

.woocommerce-noreviews {
  padding-left: 20px;
  border-left-color: rgba(0, 0, 0, 0.15);
}

.woocommerce-noreviews:before {
  display: none;
}

.onsale {
  z-index: 10;
  position: absolute;
  top: 0;
  right: 0;
  text-transform: uppercase;
  font-size: 13px;
  text-align: center;
  background-color: #259bea;
  padding: 3px 12px;
  color: #fff;
}

.item .onsale {
  border-top-right-radius: 3px;
  top: 15px;
  right: 15px;
}

.images .onsale {
  right: auto;
  border-top-left-radius: 3px;
  margin-left: 4px;
  margin-top: 4px;
}

/* Breadcrumps
========================================= */

.breadcrumb,
.woocommerce-breadcrumb {
  padding-bottom: 15px;
  border-bottom: 1px solid #dddddd;
  margin-bottom: 45px;
  color: #8e8e8e;
}

.breadcrumb a,
.woocommerce-breadcrumb a {
  color: #666;
}

.breadcrumb > span,
.woocommerce-breadcrumb > span {
  margin: 0 6px;
}

/* Shop Page
========================================= */

.shop-actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 0 25px;
  padding: 10px;
  background-color: #f6f9fc;
  line-height: normal;
  border-radius: 3px;
}

.shop-actions a {
  color: #666;
}

.shop-actions .woocommerce-result-count,
.shop-actions .product-number {
  display: inline-block;
  margin: 0;
}

.shop-actions .product-number {
  margin-left: 10px;
}

.shop-actions .product-number a {
  margin: 0 2px;
}

.shop-actions .product-number a.product-number-active {
  color: #259bea;
}

.shop-action-results {
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
  text-align: center;
}

.shop-actions-no-filter .shop-action-results {
  text-align: left;
}

.shop-filter-toggle {
  margin-right: 15px;
  color: #666;
  padding: 8px 10px;
  border-radius: 3px;
  background-color: #fff;
}

.shop-filter-toggle i {
  margin-right: 5px;
  font-size: 14px;
  color: #259bea;
}

.woocommerce-ordering {
  position: relative;
  margin: 0;
}

.woocommerce-ordering select {
  color: #4a4a4a;
  border: 0;
}

.item-product {
  padding-bottom: 15px;
}

.item-product .button {
  padding: 9px 22px;
  font-size: 14px;
  margin: 10px 0 0;
  position: relative;
  width: 100%;
  white-space: normal;
}
.disabledbutton {
    pointer-events: none;
    opacity: 0.4;
}
.item-product .button.loading {
  opacity: 0.5;
  padding-right: 2.618em;
}

.item-product .button.loading::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f110";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  top: 11px;
  right: 11px;
  -webkit-animation: rot 0.8s infinite linear;
  animation: rot 0.8s infinite linear;
}

.item-product .added_to_cart {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  text-transform: none;
  color: #fff;
  background-color: #259bea;
  border: 0;
  font-family: inherit;
  border-radius: 3px;
  font-weight: 600;
  padding: 9px 22px;
  font-size: 14px;
  margin: 10px 0 0;
}

.item-product .added_to_cart.disabled,
.item-product .added_to_cart:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

.item-product .added_to_cart:hover {
  text-decoration: none;
  color: #fff;
  background-color: #52dbee;
}

.item-product .added_to_cart:focus {
  outline: 0;
  box-shadow: 0 0 10px rgba(240, 136, 4, 0.7);
}

.item-product .button,
.item-product .added_to_cart {
  padding: 9px 22px;
  font-size: 14px;
}

.ajax_add_to_cart.added {
  display: none;
}
/* Product Page
========================================= */

.woocommerce-product-gallery {
  position: relative;
}

.woocommerce-product-gallery img {
  display: block;
  width: 100%;
  height: auto;
  box-shadow: none;
  outline: 0;
  border-radius: 3px;
}

.woocommerce-product-gallery .thumbnails {
  padding-top: 1em;
}

.woocommerce-product-gallery
  .flex-viewport
  .woocommerce-product-gallery__wrapper {
  border: 0;
}

.woocommerce-product-gallery .flex-control-thumbs {
  overflow: hidden;
  zoom: 1;
  margin: 10px -15px 0;
  padding: 0;
}

 .woocommerce-product-gallery .flex-control-thumbs li {
  width: 25%;
  float: left;
  margin: 0;
  padding: 0 15px;
  list-style: none;
  position: relative;
}

 .woocommerce-product-gallery .flex-control-thumbs li img {
  cursor: pointer;
  opacity: 0.5;
  margin: 0;
  border: 1px solid #dddddd;
}

.woocommerce-product-gallery .flex-control-thumbs li img.flex-active,
.woocommerce-product-gallery .flex-control-thumbs li img:hover {
  opacity: 1;
}

.woocommerce-product-gallery__wrapper {
  -webkit-transition: all cubic-bezier(0.795, -0.035, 0, 1) 0.5s;
  transition: all cubic-bezier(0.795, -0.035, 0, 1) 0.5s;
  margin: 0;
  padding: 0;
  border-radius: 3px;
}

.woocommerce-product-gallery__image:nth-child(n + 2) {
  width: 25%;
  display: inline-block;
}

.woocommerce-product-gallery__trigger {
  position: absolute;
  top: 0.5em;
  right: 0.5em;
  font-size: 2em;
  z-index: 9;
  width: 36px;
  height: 36px;
  background: #fff;
  text-indent: -9999px;
  border-radius: 100%;
  box-sizing: content-box;
  outline: 0;
}

.woocommerce-product-gallery__trigger:before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  border: 2px solid #000;
  border-radius: 100%;
  position: absolute;
  top: 9px;
  left: 9px;
  box-sizing: content-box;
}

.woocommerce-product-gallery__trigger:after {
  content: "";
  display: block;
  width: 2px;
  height: 8px;
  background: #000;
  border-radius: 6px;
  position: absolute;
  top: 19px;
  left: 22px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  box-sizing: content-box;
}

.woocommerce-product-gallery__trigger:active,
.woocommerce-product-gallery__trigger:focus {
  outline: 0;
}

 .woocommerce-product-gallery--columns-3
  .flex-control-thumbs
  li:nth-child(3n + 1) {
  clear: left;
}

 .woocommerce-product-gallery--columns-4
  .flex-control-thumbs
  li:nth-child(4n + 1) {
  clear: left;
}

 .woocommerce-product-gallery--columns-5
  .flex-control-thumbs
  li:nth-child(5n + 1) {
  clear: left;
}

.images {
  position: relative;
}

.woocommerce-main-image {
  display: block;
}

.type-product .thumbnails {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.type-product .thumbnails a {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
  margin-top: 30px;
}

.type-product .thumbnails a img {
  width: 100%;
}

.type-product .entry-summary {
  margin-bottom: 40px;
}

.price ins {
  text-decoration: none;
}

.entry-summary .product_title,
.single-product .product_title.entry-title {
  font-size: 28px;
  margin: 0 0 15px;
  letter-spacing: -0.01em;
  color: #000;
  font-weight: bold;
}

.product_subheadline span {
    position: relative;
    top: 0;
    font-size: 14px;
    display: inline-block;
    height: 26px;
    line-height: 25px;
    padding: 0 9px;
    border-radius: 4px;
    margin: 0 5px;
    color: #8B4400;
    background: #FFDF86;
    font-weight: 700;
    overflow: hidden;
}
.product_subheadline span:before {
    animation: shiny 2s ease-in-out infinite;
    background-color: #fff;
    content: "";
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
}
@keyframes shiny {
    0% {
        opacity: 0;
        transform: scale(0) rotate(45deg)
    }

    80% {
        opacity: .5;
        transform: scale(0) rotate(45deg)
    }

    81% {
        opacity: 1;
        transform: scale(4) rotate(45deg)
    }

    to {
        opacity: 0;
        transform: scale(50) rotate(45deg)
    }
}
.woocommerce-product-rating {
  margin: 30px 0;
  line-height: normal;
}

.entry-summary .woocommerce-product-rating,
.single-product .main .row .woocommerce-product-rating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 0 10px;
}

.entry-summary .woocommerce-product-rating .woocommerce-review-link,
.single-product
  .main
  .row
  .woocommerce-product-rating
  .woocommerce-review-link {
  font-size: 14px;
  margin-left: 7px;
  color: #8e8e8e;
}

.woocommerce-product-rating .woocommerce-review-link {
  display : none;
}

.product_meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 5px;
}

.product_meta > span {
  margin-bottom: 3px;
}

.product_meta a {
  color: #666;
}

.product-availability {
  margin-bottom: 10px;
}

.product-availability .stock {
  display: inline-block;
  margin: 0;
}

.product-availability .in-stock {
  color: #0d9502;
}

.product-availability .out-of-stock {
  color: #259bea;
}

.entry-product-info {
  padding: 30px;
  border-radius: 3px;
  background-color: #f6f9fc;
  margin-bottom: 70px;
}

.entry-product-info .price {
  font-size: 36px;
  color: #666;
  margin: 0 0 15px;
  font-weight: 900;
  letter-spacing: -0.01em;
}

.entry-summary .price,
.single-product .main .row p.price {
  display: inline-block;
  font-size: 28px;
  font-weight: bold;
  color: #259bea;
}
.price-savings {
  display: inline-block;
  float: left;
  border-radius: 3px;
  font-size: 15px;
  line-height: 31px;
  padding: 0 7px;
  margin-right: 5px;
  margin-top: 5px;
  background-color: #ff323b;
  background-image: url(assets/img/button-bg.svg);
  color: #fff;
  font-weight: bold;
  background-position-x: center;
}

.entry-summary .price del,
.single-product .main .row p.price del {
  color: #aeaeae;
  font-size: 21px;
  font-weight: normal;
}
.entry-product-info .price del {
  font-size: 20px;
  margin-right: 5px;
  display: block;
  line-height: normal;
}

.entry-product-info .price ins {
  text-decoration: none;
}

.tagged_as {
  display: block;
  margin-top: 20px;
}

.tagged_as a {
  color: #666;
}

.woocommerce-grouped-product-list td {
  padding-top: 8px;
  padding-bottom: 8px;
}

.woocommerce-grouped-product-list tr td:first-child {
  width: 120px;
}

.woocommerce-grouped-product-list .product-availability {
  font-size: 14px;
}

.woocommerce-grouped-product-list-item__quantity .button {
  padding: 9px 22px;
  font-size: 14px;
}

.woocommerce-grouped-product-list-item__price {
  text-align: right;
}

.woocommerce-grouped-product-list-item__price .woocommerce-Price-amount {
  color: #259bea;
  font-weight: 700;
  letter-spacing: -0.01em;
}

.woocommerce-grouped-product-list-item__price ins {
  text-decoration: none;
}

.woocommerce-grouped-product-list-item__price del {
  color: #259bea;
  opacity: 0.6;
  margin-right: 5px;
}

.woocommerce-grouped-product-list-item__price del .woocommerce-Price-amount {
  font-weight: 400;
}

.woocommerce-grouped-product-list-item__price .product-availability {
  margin: 0;
}

.woocommerce-grouped-product-list-item__label label {
  margin: 0;
}

.woocommerce-grouped-product-list-item__label a {
  color: #666;
}

/* Add to cart
========================================= */

.quantity {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  line-height: normal;
  margin: 0 0 20px;
}

.group_table .quantity {
  margin: 0 10px 0 0;
  vertical-align: middle;
}

.quantity label {
  margin: 0 10px 0 0;
}

.qty {
  text-align: center;
  padding: 0;
  margin: 0;
  max-width: 120px;
}

.entry-summary .qty {
  -moz-appearance: textfield !important;
}

.entry-summary .qty::-webkit-outer-spin-button,
.entry-summary .qty::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
}

.group_table .qty {
  width: 46px;
}

button.single_add_to_cart_button {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  text-transform: none;
  color: #fff;
  background-color: #259bea;
  border: 0;
  font-family: inherit;
  border-radius: 3px;
  font-weight: 600;
  padding: 13px 28px;
  font-size: 16px;
  width: 100%;
  max-width: 260px;
}

button.single_add_to_cart_button.disabled,
button.single_add_to_cart_button:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

button.single_add_to_cart_button:hover {
  text-decoration: none;
  color: #fff;
  background-color: #52dbee;
}

button.single_add_to_cart_button:focus {
  outline: 0;
  box-shadow: 0 0 10px rgba(240, 136, 4, 0.7);
}

.product-type-external button.single_add_to_cart_button {
  position: relative;
  padding-right: 30px;
}

.product-type-external button.single_add_to_cart_button::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f35d";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 10px;
  position: absolute;
  top: 5px;
  right: 5px;
}

.variations {
  width: auto;
}

.variations td {
  padding: 5px;
  border: 0;
  position: relative;
}

.variations td.label {
  line-height: normal;
  max-width: 70px;
}

.variations td.label label {
  margin: 0;
}

.variations td.value {
  width: 170px;
}

.woocommerce-variation-price {
  margin: 15px 0;
}

.reset_variations {
  position: absolute;
  color: #666;
  left: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  margin: 0 0 0 5px;
}

.single-product-table-wrapper {
  padding: 20px;
  margin: 15px 0 30px;
  border: 1px solid #dddddd;
  border-radius: 3px;
  background-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.05);
}

.sticky-atc-btn {
  display: none;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  padding: 10px 0;
  text-align: center;
  border-top: 1px solid #e0e0e0;
}
.sticky-atc-btn a {
  display: inline-block;
  padding: 0 100px;
  animation: shake-x 4s ease infinite;
  width: auto;
  height: 45px;
  font-size: 15px;
  line-height: 45px;
  color: #fff;
  background-color: #259bea;
  background-image: url(assets/img/button-bg.svg);
  font-weight: bold;

  border-radius: 4px;
}
.sticky-atc-btn a:hover {
  color: #fff;
}
@keyframes shake-x {
  0%,
  100% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
}

/* Star Ratings
========================================= */

.star-rating {
  overflow: hidden;
  display: inline-block;
  position: relative;
  height: 1em;
  line-height: 1em;
  font-size: 12px;
  width: 68px;
  color: #ffc108;
}

.star-rating::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
}

.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
.entry-summary .star-rating {
  font-size: 15px !important;
  width: 85px;
}
/* Tabs
========================================= */

.woocommerce-tabs {
  border-radius: 3px;
  background-color: #fff;
}

.wc-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  padding: 0 0 20px;
  border-bottom: 1px solid #dddddd;
}

.wc-tabs li {
  display: inline-block;
  margin: 0;
  padding: 0;
}

.wc-tabs li.active a {
  color: #666;
}

.woocommerce-tabs-container {
  margin: 30px 0;
}
.wc-tabs a {
  color: #8e8e8e;
  border-bottom-color: transparent;
  -webkit-transition: border-color 0.18s ease;
  transition: border-color 0.18s ease;
}

.woocommerce-Tabs-panel {
  padding: 20px 0 0;
}

.woocommerce-Tabs-panel > h2:first-of-type {
  display: none;
}

.woocommerce-Tabs-panel > p:last-child,
.woocommerce-Tabs-panel > table:last-child {
  margin-bottom: 0;
}

.woocommerce-Tabs-panel.entry-content h2,
.woocommerce-Tabs-panel.entry-content h3,
.woocommerce-Tabs-panel.entry-content h4,
.woocommerce-Tabs-panel.entry-content h5,
.woocommerce-Tabs-panel.entry-content h6 {
  margin: 20px 0 10px;
}

.shop_attributes th,
.shop_attributes td {
  vertical-align: middle;
}

.shop_attributes p {
  margin-bottom: 0;
}

/* Reviews */

#review_form_wrapper {
  background: #f7f8f8;
  padding: 10px 20px;
}
input#review_image {
  border: none;
  background: #f7f8f8;
}
.woocommerce-Reviews .commentlist {
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 25px;
}
.woocommerce-Reviews #comments {
  margin: 0;
}

.woocommerce-Reviews #comments .woocommerce-Reviews-title {
  display: none;
}

.woocommerce-Reviews .comment-text {
  font-size: 14px;
}

.woocommerce-Reviews .comment-text img {
  aspect-ratio: 1 / 1;
  object-fit: cover; 
  width: 100%;
}

.woocommerce-Reviews .comment-text p {
  margin-bottom: 10px;
}

.woocommerce-Reviews li {
  margin-bottom: 20px;
  border: 1px solid #efefef;
  border-radius: 3px;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.15);
}

.woocommerce-Reviews .avatar {
  display: none;
  width: 64px;
  height: 64px;
  float: left;
  margin: 0 25px 15px 0;
  overflow: visible;
  border-radius: 3px;
}

.woocommerce-Reviews .description {
  padding: 0 7px 5px;
  overflow: hidden;
}

.woocommerce-Reviews .meta {
  text-align: center;
  font-size: 16px;
  margin: 10px 0;
}

.woocommerce-Reviews .star-rating {
  display: block;
  margin: 10px auto;
}

.woocommerce-review__dash,
.woocommerce-review__published-date {
  display: none;
}
.woocommerce-Reviews .comment-respond {
  margin-top: 30px;
}

.comment-form-rating a {
  display: inline-block;
  position: relative;
  width: 15px;
  text-indent: -9999px;
  border: none;
  margin-right: 12px;
  color: #ffc108;
}

.comment-form-rating a::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
  font-size: 13px;
  opacity: 1;
  display: inline-block;
  text-indent: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.comment-form-rating a.active::after {
  font-weight: 900;
}

.comment-form-rating .star-2 {
  width: 26px;
}

.comment-form-rating .star-2::after {
  content: "\f005\f005";
  font-weight: 400;
}

.comment-form-rating .star-2.active::after {
  font-weight: 900;
}

.comment-form-rating .star-3 {
  width: 39px;
}

.comment-form-rating .star-3::after {
  content: "\f005\f005\f005";
  font-weight: 400;
}

.comment-form-rating .star-3.active::after {
  font-weight: 900;
}

.comment-form-rating .star-4 {
  width: 52px;
}

.comment-form-rating .star-4::after {
  content: "\f005\f005\f005\f005";
  font-weight: 400;
}

.comment-form-rating .star-4.active::after {
  font-weight: 900;
}

.comment-form-rating .star-5 {
  width: 65px;
}

.comment-form-rating .star-5::after {
  content: "\f005\f005\f005\f005\f005";
  font-weight: 400;
}

.comment-form-rating .star-5.active::after {
  font-weight: 900;
}

/* Cart Page
========================================= */

.shop_table td {
  padding: 10px 5px;
}

.shop_table.cart {
  border: 1px solid #dddddd;
  border-bottom: 0;
  border-right: 0;
}
.woocommerce-cart .cart-collaterals .cross-sells h2 {
  margin-bottom: 15px;
}
.shop_table.cart th,
.shop_table.cart td {
  padding: 10px;
  border-bottom: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
}

.shop_table .product-remove {
  text-align: center;
}

.shop_table .remove {
  font-size: 24px;
  color: #666;
}

.shop_table .product-thumbnail {
  text-align: center;
}

.shop_table .product-thumbnail img {
  width: 60px;
}

.shop_table .product-name a {
  color: #4a4a4a;
}

.shop_table .product-name a:hover {
  color: #666;
}

.shop_table .product-price {
  text-align: center;
}

.shop_table .product-quantity {
  text-align: center;
}

.shop_table .product-quantity .quantity {
  margin: 0;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.shop_table .product-subtotal {
  text-align: right;
}

.shop_table .product-subtotal .woocommerce-Price-amount {
  color: #666;
  font-weight: bold;
}

.shop_table .coupon {
  width: 280px;
  position: relative;
  float: left;
  height: 46px;
}

.shop_table .coupon label {
  display: none;
}

.shop_table .coupon input,
.shop_table .coupon button {
  height: 100%;
}

.shop_table .coupon button[type="submit"] {
  position: absolute;
  height: 100%;
  min-width: 0;
  top: 0;
  right: 0;
  font-size: 11px;
  padding: 5px 14px;
  border: 0;
}

.shop_table [name="update_cart"] {
  float: right;
}

.cart-collaterals {
  margin-top: 50px;
}

.cart-collaterals .shop_table {
  border: 1px solid #dddddd;
  border-bottom: 0;
  border-right: 0;
}

.cart-collaterals .shop_table th,
.cart-collaterals .shop_table td {
  padding: 10px;
  border-bottom: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
}

.cart-collaterals .shop_table th {
  line-height: normal;
  width: 25%;
}

.wc-proceed-to-checkout a {
  padding: 18px 38px;
  font-size: 16px;
}

.woocommerce-remove-coupon {
  margin-left: 5px;
}

#billing_country_field,
#ship-to-different-address {
  display: none;
}
#shipping_method {
  list-style: none;
  margin: 0 0 5px;
  padding: 0;
}

.shipping label {
  display: inline-block;
  margin-left: 5px;
}

.shipping input[type="radio"] {
  display: inline-block;
}

.woocommerce-shipping-calculator [type="submit"] {
  padding: 9px 22px;
  font-size: 14px;
  width: 100%;
}

.woocommerce-shipping-calculator p {
  margin: 0 0 5px;
}

.woocommerce-shipping-calculator select {
  width: 100%;
}

.shipping-calculator-button {
  color: #666;
  font-size: 12px;
}

.shipping-calculator-button::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f107";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  margin-left: 3px;
}

.shipping-calculator-form {
  width: 100%;
}

.variation {
  margin: 0;
  font-size: 12px;
}

.variation dt,
.variation dd,
.variation p {
  font-weight: normal;
  display: inline-block;
  margin: 0;
}

.variation dd {
  margin-right: 3px;
}

.backorder_notification {
  font-size: 12px;
}

/* Checkout Page
========================================= */

.woocommerce-checkout .woocommerce-info {
  margin-bottom: 20px;
}

.woocommerce-checkout .login {
  margin-bottom: 30px;
}

.woocommerce-checkout .login::after {
  content: "";
  display: table;
  clear: both;
}

.woocommerce-checkout .login .form-row-last {
  margin-right: 0;
}

.woocommerce-checkout .login .button {
  padding: 9px 22px;
  font-size: 14px;
}

.woocommerce-checkout .login .woocommerce-form__label-for-checkbox {
  display: inline-block;
  margin-left: 10px;
}

.woocommerce-checkout .checkout_coupon {
  margin-bottom: 30px;
}

#customer-details {
  margin: 0 0 25px;
}

.woocommerce-billing-fields p,
.woocommerce-shipping-fields p,
.woocommerce-additional-field p {
  margin: 0 0 15px;
}

.woocommerce-billing-fields .input-text,
.woocommerce-shipping-fields .input-text,
.woocommerce-additional-field .input-text {
  width: 100%;
}

.woocommerce-billing-fields abbr,
.woocommerce-shipping-fields abbr,
.woocommerce-additional-field abbr {
  text-decoration: none;
  border: none;
}

.woocommerce-shipping-fields .woocommerce-form__label-for-checkbox {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.woocommerce-shipping-fields .woocommerce-form__label-for-checkbox input {
  margin: 0 10px 0 0;
}

#order_review_heading {
  padding-top: 35px;
  margin-top: 20px;
  border-top: 2px solid #dddddd;
}

#order_review .shop_table td,
#order_review .shop_table th,
.order_details td,
.order_details th {
  text-align: right;
  padding-left: 0;
  padding-right: 0;
}

#order_review .shop_table .product-total,
.order_details .product-total {
  text-align: right;
}

#order_review .shop_table .product-name,
.order_details .product-name {
  text-align: left;
}

#order_review .shop_table .product-quantity,
.order_details .product-quantity {
  font-weight: normal;
}

#order_review .shop_table tfoot tr,
.order_details tfoot tr {
  border: 0;
}

#order_review .shop_table tfoot td,
#order_review .shop_table tfoot th,
.order_details tfoot td,
.order_details tfoot th {
  padding: 9px 0 0;
  text-align: right;
  border: 0;
}

.wc_payment_methods {
  list-style: none;
  padding: 0;
  margin: 0 0 30px;
}

.wc_payment_method {
  padding: 10px 0;
  border-bottom: 1px solid #dddddd;
}
.wc_payment_method:last-child {
  border: none;
}
.wc_payment_method label {
  display: inline-block;
  margin: 0 0 0 5px;
}

.payment_method_paypal img {
  width: 100px;
  margin: -2px 15px 0;
}

.about_paypal {
  font-size: 12px;
}

.payment_box {
  padding: 15px;
  border: 1px solid #dddddd;
  margin: 5px 0 0;
}

.payment_box p {
  margin: 0;
}

.woocommerce-table--order-details thead {
    color: #fff;
    background: #259bea;
}

.woocommerce-checkout-review-order-table tfoot td,
.woocommerce-table--order-details tfoot td {
  width: 320px;
  padding-left: 15px;
}

.woocommerce-checkout-review-order-table tfoot td p,
.woocommerce-table--order-details tfoot td p {
  margin: 0;
  font-size: 14px;
}

.place-order .terms {
  display: inline-block;
  margin-left: 20px;
}

.place-order label[for="terms"] {
  display: inline-block;
  margin-right: 5px;
}

.select2-container .select2-selection--single {
  border-color: #cccccc;
  height: 35px;
}

.select2-search__field {
  height: 32px;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 35px;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 100%;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #cccccc;
}

.select2-dropdown {
  border-color: #cccccc;
}

/* Order Received Page
========================================= */
.woocommerce-order-received .woocommerce-breadcrumb,
.woocommerce-thankyou-order-received,
.woocommerce-thankyou-order-details {
  display: none;
}
.woocommerce-order-received .customer_details {
  padding: 25px;
  border: 1px solid #dddddd;
}

.woocommerce-order-received .customer_details td,
.woocommerce-order-received .customer_details th {
  border: 0;
  padding: 0;
}
.woocommerce-order-received .woocommerce-order-details {
  max-width: 600px;
  margin: 0 auto;
}
.woocommerce-order-details__title {
  text-align: center;
}
.woocommerce-thankyou-order-details,
.wc-bacs-bank-details {
  list-style: none;
  margin: 15px 0 30px;
  padding: 25px;
  border: 1px solid #dddddd;
}

.woocommerce-thankyou-order-details::after,
.wc-bacs-bank-details::after {
  content: "";
  display: table;
  clear: both;
}

.woocommerce-thankyou-order-details li,
.wc-bacs-bank-details li {
  margin-bottom: 10px;
}

.woocommerce-thankyou-order-details li strong,
.wc-bacs-bank-details li strong {
  display: block;
}

#wc-bacs-bank-details-heading {
  margin-top: 30px;
}

#wc-bacs-bank-details-heading + h3 {
  font-size: 14px;
}

.wc-bacs-bank-details {
  margin-bottom: 30px;
}

/* My Account - General
========================================= */

.woocommerce-account .woocommerce:not(.widget) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.woocommerce-account .woocommerce:not(.widget) > h2,
.woocommerce-account .woocommerce:not(.widget) > form.login {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.woocommerce-account .woocommerce:not(.widget) > .woocommerce-message,
.woocommerce-account .woocommerce:not(.widget) > .woocommerce-error,
.woocommerce-account .woocommerce:not(.widget) > .woocommerce-info,
.woocommerce-account .woocommerce:not(.widget) > .woocommerce-noreviews {
  margin-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc(100% - 30px);
  flex: 0 0 calc(100% - 30px);
  max-width: calc(100% - 30px);
}

.woocommerce-MyAccount-navigation {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
  margin-bottom: 30px;
}

.woocommerce-MyAccount-navigation ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link {
  display: block;
}

.woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a {
  padding: 10px 0;
  display: block;
  border-bottom: 1px solid #dddddd;
  position: relative;
  color: #4a4a4a;
}

.woocommerce-MyAccount-navigation
  .woocommerce-MyAccount-navigation-link
  a:hover {
  color: #259bea;
}

.woocommerce-MyAccount-navigation
  .woocommerce-MyAccount-navigation-link
  a::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  top: 10px;
  right: 5px;
}

.woocommerce-MyAccount-navigation
  .woocommerce-MyAccount-navigation-link.is-active
  a {
  font-weight: bold;
}

.woocommerce-MyAccount-navigation
  .woocommerce-MyAccount-navigation-link--customer-logout
  a::after {
  content: "";
}

.woocommerce-MyAccount-content {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.woocommerce-MyAccount-content mark {
  color: #4a4a4a;
}

/* My Account - Orders
========================================= */

.woocommerce-MyAccount-orders .button.view {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  text-transform: none;
  color: #fff;
  background-color: #259bea;
  border: 0;
  font-family: inherit;
  border-radius: 3px;
  font-weight: 600;
  padding: 9px 22px;
  font-size: 14px;
}

.woocommerce-MyAccount-orders .button.view.disabled,
.woocommerce-MyAccount-orders .button.view:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

.woocommerce-MyAccount-orders .button.view:hover {
  text-decoration: none;
  color: #fff;
  background-color: #52dbee;
}

.woocommerce-MyAccount-orders .button.view:focus {
  outline: 0;
  box-shadow: 0 0 10px rgba(240, 136, 4, 0.7);
}

/* My Account - Downloads
========================================= */

.woocommerce-MyAccount-downloads .download-file {
  width: 50%;
}

.woocommerce-MyAccount-downloads .download-file a {
  color: #4a4a4a;
}

.woocommerce-MyAccount-downloads .woocommerce-Button.download {
  padding: 9px 22px;
  font-size: 14px;
}

/* My Account - Details
========================================= */

.woocommerce-EditAccountForm fieldset {
  padding: 25px;
  border: 2px solid #dddddd;
}

.woocommerce-EditAccountForm legend {
  padding: 0 15px;
}

/* My Account - Addresses
========================================= */

.woocommerce-Addresses {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.woocommerce-Address {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.woocommerce-Address-title {
  position: relative;
}

.woocommerce-Address-title a {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 12px;
  color: #4a4a4a;
}

/* My Account - Login
========================================= */

.wc-form-login {
  width: 480px;
  max-width: 480px;
  margin: 0 auto 100px;
  padding: 50px;
  border: 1px solid #dddddd;
}

.wc-form-login input[type="submit"] {
  margin: 0 0 15px;
}

.wc-form-login h2 {
  margin: 0 0 20px;
}

.wc-form-login.with-register {
  width: 740px;
  max-width: 740px;
}

.wc-form-login .woocommerce-form__label-for-checkbox {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 13px;
  margin-left: 10px;
}

.inline input {
  margin-right: 5px;
}

.woocommerce-LostPassword {
  margin: 0;
  font-size: 16px;
}

.woocommerce-privacy-policy-text {
  font-size: 13px;
}

/* WIDGET: WooCommerce Price Range
========================================= */

.price_slider_wrapper {
  margin-top: 30px;
}

.price_slider {
  position: relative;
  text-align: left;
  height: 4px;
  width: calc(100% - 14px);
  margin: 0 auto 30px;
  background: #dddddd;
}

.price_slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  background: #dddddd;
  height: 4px;
  cursor: pointer;
}

.price_slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 16px;
  border-radius: 3px;
  height: 25px;
  margin-top: -10px;
  margin-left: -7px;
  cursor: pointer;
  outline: none;
  background-color: #666;
  -ms-touch-action: none;
  touch-action: none;
}

.price_slider .ui-slider-handle:active {
  outline: none;
}

.price_slider .ui-slider-handle::after {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f0c9";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 6px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 1;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
}

.price_slider_amount {
  position: relative;
}

.price_slider_wrapper .price_label {
  position: absolute;
  right: 0;
  top: 0;
}

.price_slider_wrapper button {
  padding: 9px 22px;
  font-size: 14px;
}

/* WIDGET: WooCommerce Products
========================================= */

.product_list_widget {
  list-style: none;
  margin: 0;
  padding: 0;
}

.product_list_widget li {
  margin-bottom: 15px;
  position: relative;
}

.product_list_widget li::after {
  content: "";
  display: table;
  clear: both;
}

.product_list_widget li img {
  width: 80px;
}

.product_list_widget .product-thumb {
  float: left;
  margin-right: 15px;
}

.product_list_widget .product-thumb img {
  border-radius: 3px;
  border: 1px solid #dddddd;
}

.product_list_widget .product-content {
  overflow: hidden;
}

.product_list_widget .product-title {
  line-height: normal;
  display: block;
  margin-bottom: 0;
  font-size: 14px;
  color: #666;
  letter-spacing: -0.01em;
}

.footer-widgets .product_list_widget .product-title {
  color: #fff;
}

.product_list_widget .product-title:hover {
  color: #259bea;
}

.product_list_widget .reviewer {
  font-size: 12px;
  display: block;
  margin-top: 7px;
}

.product_list_widget .star-rating {
  display: block;
  margin: 10px 0 5px;
}

.product_list_widget .woocommerce-Price-amount {
  color: #259bea;
  letter-spacing: -0.01em;
  font-weight: 600;
  font-size: 14px;
}

.product_list_widget ins {
  text-decoration: none;
}

.product_list_widget del {
  color: #259bea;
  opacity: 0.5;
}

.product_list_widget .remove {
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 16px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 14px;
  text-indent: -1px;
  background-color: #ff786d;
  color: #fff;
  border-radius: 3px;
}

.product_list_widget .quantity {
  display: inline;
  margin: 0;
  font-size: 14px;
  color: #8e8e8e;
}

/* WIDGET: WooCommerce Cart Widget
========================================= */

.header-mini-cart .widget_shopping_cart {
  margin-bottom: 0;
}
.widget_shopping_cart .buttons:not(.woocommerce-mini-cart__buttons)  {
  display: none;
}
.widget_shopping_cart .buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.widget_shopping_cart .buttons .button {
  padding: 9px 22px;
  font-size: 14px;
  width: 50%;
  margin: 0 15px 0 0;
}

.widget_shopping_cart .buttons .button:last-child {
  margin-right: 0;
}

.widget_shopping_cart li.empty {
  margin: 0;
}

.header .widget_shopping_cart li.empty {
  text-align: center;
  border: 2px solid #dddddd;
  padding: 25px;
}

.woocommerce-mini-cart__total {
  color: #8e8e8e;
  margin: 25px 0 15px;
  letter-spacing: -0.01em;
}

.woocommerce-mini-cart__total strong {
  font-weight: normal;
}

.woocommerce-mini-cart__total .woocommerce-Price-amount {
  color: #259bea;
  font-weight: 600;
}

.woocommerce-mini-cart__empty-message {
  text-align: center;
  padding: 15px 20px;
  border: 1px solid #dddddd;
  border-radius: 3px;
}

/* WIDGET: WooCommerce Layered Nav Widget
========================================= */

.widget_layered_nav_filters ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.widget_layered_nav_filters li {
  display: inline-block;
  margin: 0 5px 5px 0;
}

.widget_layered_nav_filters a {
  display: inline-block;
  background-color: #f6f9fc;
  border-radius: 2px;
  color: #4a4a4a;
  font-size: 12px;
  padding: 2px 10px;
  position: relative;
}

.widget_layered_nav_filters a::before {
  content: "\00d7";
  margin-right: 5px;
  font-size: 14px;
}

.widget_layered_nav_filters a:hover::before {
  color: #ff786d;
}

.widget_layered_nav li.chosen a::before {
  content: "\00d7";
  margin-right: 5px;
  font-size: 14px;
}

.widget_layered_nav li.chosen a:hover::before {
  color: #ff786d;
}

/* Ratings
========================================= */

.wc-block-grid__products .wc-block-grid__product-rating .star-rating,
.wc-block-review-list-item__rating .wc-block-review-list-item__rating__stars {
  overflow: hidden;
  display: inline-block;
  position: relative;
  height: 1.2em;
  line-height: 1.2em;
  font-size: 12px;
  width: 68px;
}

.wc-block-grid__products .wc-block-grid__product-rating .star-rating::before,
.wc-block-review-list-item__rating
  .wc-block-review-list-item__rating__stars::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  color: #259bea;
  opacity: 0.75;
}

.wc-block-grid__products .wc-block-grid__product-rating .star-rating span,
.wc-block-review-list-item__rating
  .wc-block-review-list-item__rating__stars
  span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}

.wc-block-grid__products
  .wc-block-grid__product-rating
  .star-rating
  span::before,
.wc-block-review-list-item__rating
  .wc-block-review-list-item__rating__stars
  span::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f005\f005\f005\f005\f005";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  top: 0;
  position: absolute;
  left: 0;
  color: #259bea;
}

/* Product block
========================================= */

.wc-block-grid__products {
  margin-bottom: 0;
}

.wc-block-grid__products .wc-block-grid__product-add-to-cart a.added::after,
.wc-block-grid__products .wc-block-grid__product-add-to-cart a.loading::after {
  display: none;
}

.wc-block-grid__products .add_to_cart_button,
.wc-block-grid__products .added_to_cart {
  display: inline-block;
  margin: 0;
  line-height: normal;
  box-shadow: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  transition: 0.18s background-color ease, 0.18s color ease,
    0.18s border-color ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: auto;
  height: auto;
  text-transform: none;
  color: #fff;
  background-color: #259bea;
  border: 0;
  font-family: inherit;
  border-radius: 3px;
  font-weight: 600;
  padding: 9px 22px;
  font-size: 14px;
  position: relative;
  white-space: normal;
}

.wc-block-grid__products .add_to_cart_button.disabled,
.wc-block-grid__products .add_to_cart_button:disabled,
.wc-block-grid__products .added_to_cart.disabled,
.wc-block-grid__products .added_to_cart:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

.wc-block-grid__products .add_to_cart_button:hover,
.wc-block-grid__products .added_to_cart:hover {
  text-decoration: none;
  color: #fff;
  background-color: #52dbee;
}

.wc-block-grid__products .add_to_cart_button:focus,
.wc-block-grid__products .added_to_cart:focus {
  outline: 0;
  box-shadow: 0 0 10px rgba(240, 136, 4, 0.7);
}

.wc-block-grid__products .add_to_cart_button.loading,
.wc-block-grid__products .added_to_cart.loading {
  opacity: 0.5;
  padding-right: 2.618em;
}

.wc-block-grid__products .add_to_cart_button.loading::before,
.wc-block-grid__products .added_to_cart.loading::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  content: "\f110";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  top: 9px;
  right: 11px;
  -webkit-animation: rot 0.8s infinite linear;
  animation: rot 0.8s infinite linear;
}

.wc-block-grid__products .wc-block-grid__product-title {
  margin: 15px 0 10px;
  color: #666;
  font-size: 16px;
  font-weight: 400;
}

.wc-block-grid__products .wc-block-grid__product-title:hover {
  color: #259bea;
}

.wc-block-grid__products .wc-block-grid__product-price {
  font-size: 16px;
  display: block;
  margin: 5px 0 15px;
  color: #259bea;
  font-weight: 600;
}

.wc-block-grid__products .wc-block-grid__product-price del {
  font-weight: 400;
  opacity: 0.5;
  font-size: 15px;
  margin-right: 3px;
}

.wc-block-grid__products .wc-block-grid__product-onsale {
  z-index: 10;
  position: absolute;
  top: 5px !important;
  right: 20px;
  left: auto !important;
  width: auto;
  font-size: 13px;
  text-align: center;
  background-color: #259bea;
  padding: 3px 12px;
  color: #fff;
  text-transform: none;
  border: 0;
  margin: 0;
}

.wc-block-grid__products .wc-block-grid__product-rating {
  margin: -5px auto 10px;
}

.wc-block-grid__products > .wc-block-grid__product {
  margin-bottom: 25px;
  text-align: left;
}

/* Reviews
========================================= */

.wc-block-review-list {
  padding-left: 0;
}

.wc-block-review-list .wc-block-review-list-item__item {
  margin-bottom: 25px;
}

.wc-block-review-list .wc-block-review-list-item__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 15px;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__image {
  width: 50px;
  height: 50px;
  margin-right: 15px;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__image
  img {
  border-radius: 3px;
  border: 1px solid #dddddd;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__author,
.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__product {
  line-height: normal;
  display: block;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__author
  a,
.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__product
  a {
  color: #4a4a4a;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__author
  a:hover,
.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__product
  a:hover {
  color: #259bea;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__product
  + .wc-block-review-list-item__author {
  font-size: 12px;
  -webkit-box-ordinal-group: 4;
  -ms-flex-order: 3;
  order: 3;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__rating {
  display: block;
  margin: 10px 0 5px;
  line-height: 1em;
}

.wc-block-review-list
  .wc-block-review-list-item__info
  .wc-block-review-list-item__meta
  .wc-block-review-list-item__author
  + .wc-block-review-list-item__published-date::before {
  display: none;
}

/* Product Grid
========================================= */

.wc-block-grid .wc-block-grid__products {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.has-4-columns .wc-block-grid__products .wc-block-grid__product {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.has-3-columns .wc-block-grid__products .wc-block-grid__product {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.has-2-columns .wc-block-grid__products .wc-block-grid__product {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

/* BLOCK: Search
========================================= */

.wc-block-product-search__fields {
  position: relative;
}

.wc-block-product-search__fields .wc-block-product-search__button {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 100%;
}

.wc-block-product-search__fields .wc-block-product-search__button:hover {
  background: none !important;
  box-shadow: none !important;
  border: 0;
}

/* BLOCK: Product Categories
========================================= */

.wc-block-product-categories-list {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 14px;
}

.wc-block-product-categories-list ul {
  margin-left: 15px;
}

.wc-block-product-categories-list li {
  line-height: normal;
  display: block;
  position: relative;
}

.wc-block-product-categories-list li a {
  display: block;
  color: #4a4a4a;
  padding: 11px 30px 11px 0;
  border-bottom: 1px solid #dddddd;
}

.wc-block-product-categories-list li a:hover {
  color: #259bea;
}

.wc-block-product-categories-list
  li
  .wc-block-product-categories-list-item-count {
  position: absolute;
  right: 0;
  top: 11px;
}

.wc-block-product-categories.is-dropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.wc-block-product-categories.is-dropdown
  .wc-block-product-categories__dropdown {
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
}

.wc-block-product-categories.is-dropdown select {
  width: 100%;
}

.wc-block-product-categories.is-dropdown .wc-block-product-categories__button {
  border: 1px solid #dddddd;
  border-left: 0;
  border-radius: 0;
  padding: 6px;
}

.wc-block-product-categories.is-dropdown
  .wc-block-product-categories__button:hover {
  box-shadow: none;
}

/* BLOCK: Filter by Price
========================================= */

.wp-block-woocommerce-price-filter h3 {
  line-height: normal;
  font-size: 18px;
}

.wc-block-price-filter
  .wc-block-price-filter__range-input-wrapper
  .wc-block-price-filter__range-input-progress {
  --range-color: #dddddd;
}

.wc-block-price-filter
  .wc-block-price-filter__controls
  .wc-block-price-filter__amount {
  border-radius: 0;
  height: auto;
  padding: 7px 14px;
  text-align: center;
  margin-top: 10px;
}

.wc-block-price-filter
  .wc-block-price-filter__range-input::-webkit-slider-thumb {
  background-color: transparent;
  background-position: 0 0;
  width: 26px;
  height: 21px;
  border: 0;
  padding: 0;
  margin: 0;
  vertical-align: top;
  cursor: pointer;
  z-index: 20;
  pointer-events: auto;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='52' height='42'%3E%3Cdefs%3E%3Cpath id='a' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath id='b' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M24.3176 8.9423l-8.4163-6.1432c-1.706-1.2285-3.6395-1.8988-5.6867-1.787h-.1137c-4.8906.335-8.985 4.356-9.0987 9.2706C.8885 15.644 5.2102 20 10.6696 20h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M9 6v9m3-9v9'/%3E%3Cg fill-rule='nonzero' transform='translate(1 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23a'/%3E%3Cuse stroke='%23000' xlink:href='%23a'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M9 27v9m3-9v9'/%3E%3Cg%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M27.6824 8.9423l8.4163-6.1432c1.706-1.2285 3.6395-1.8988 5.6867-1.787h.1137c4.8906.335 8.985 4.356 9.0987 9.2706C51.1115 15.644 46.7898 20 41.3304 20h-.1137c-1.8197 0-3.6395-.6702-5.118-1.787l-8.4163-6.255c-.9099-.8935-.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M43 6v9m-3-9v9'/%3E%3C/g%3E%3Cg%3E%3Cg fill-rule='nonzero' transform='matrix(-1 0 0 1 51 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23b'/%3E%3Cuse stroke='%23000' xlink:href='%23b'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M43 27v9m-3-9v9'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: -6px 0 0 0;
}

.wc-block-price-filter
  .wc-block-price-filter__range-input::-webkit-slider-thumb:hover {
  background-position-y: 0;
  -webkit-filter: none;
  filter: none;
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.wc-block-price-filter .wc-block-price-filter__range-input::-moz-range-thumb {
  background-color: transparent;
  background-position: 0 0;
  width: 26px;
  height: 21px;
  border: 0;
  padding: 0;
  margin: 0;
  vertical-align: top;
  cursor: pointer;
  z-index: 20;
  pointer-events: auto;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='52' height='42'%3E%3Cdefs%3E%3Cpath id='a' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath id='b' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M24.3176 8.9423l-8.4163-6.1432c-1.706-1.2285-3.6395-1.8988-5.6867-1.787h-.1137c-4.8906.335-8.985 4.356-9.0987 9.2706C.8885 15.644 5.2102 20 10.6696 20h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M9 6v9m3-9v9'/%3E%3Cg fill-rule='nonzero' transform='translate(1 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23a'/%3E%3Cuse stroke='%23000' xlink:href='%23a'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M9 27v9m3-9v9'/%3E%3Cg%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M27.6824 8.9423l8.4163-6.1432c1.706-1.2285 3.6395-1.8988 5.6867-1.787h.1137c4.8906.335 8.985 4.356 9.0987 9.2706C51.1115 15.644 46.7898 20 41.3304 20h-.1137c-1.8197 0-3.6395-.6702-5.118-1.787l-8.4163-6.255c-.9099-.8935-.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M43 6v9m-3-9v9'/%3E%3C/g%3E%3Cg%3E%3Cg fill-rule='nonzero' transform='matrix(-1 0 0 1 51 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23b'/%3E%3Cuse stroke='%23000' xlink:href='%23b'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M43 27v9m-3-9v9'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.wc-block-price-filter
  .wc-block-price-filter__range-input::-moz-range-thumb:hover {
  background-position-y: 0;
  filter: none;
  transform: scale(1.1);
}

.wc-block-price-filter .wc-block-price-filter__range-input::-ms-thumb {
  background-color: transparent;
  background-position: 0 0;
  width: 26px;
  height: 21px;
  border: 0;
  padding: 0;
  margin: 0;
  vertical-align: top;
  cursor: pointer;
  z-index: 20;
  pointer-events: auto;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='52' height='42'%3E%3Cdefs%3E%3Cpath id='a' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath id='b' d='M23.3176 7.9423l-8.4163-6.1432C13.1953.5706 11.2618-.0997 9.2146.0121h-.1137C4.2103.347.1159 4.368.0022 9.2827-.1115 14.644 4.2102 19 9.6696 19h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3C/defs%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M24.3176 8.9423l-8.4163-6.1432c-1.706-1.2285-3.6395-1.8988-5.6867-1.787h-.1137c-4.8906.335-8.985 4.356-9.0987 9.2706C.8885 15.644 5.2102 20 10.6696 20h.1137c1.8197 0 3.6395-.6702 5.118-1.787l8.4163-6.255c.9099-.8935.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M9 6v9m3-9v9'/%3E%3Cg fill-rule='nonzero' transform='translate(1 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23a'/%3E%3Cuse stroke='%23000' xlink:href='%23a'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M9 27v9m3-9v9'/%3E%3Cg%3E%3Cpath fill='%23FFF' fill-rule='nonzero' stroke='%23000' d='M27.6824 8.9423l8.4163-6.1432c1.706-1.2285 3.6395-1.8988 5.6867-1.787h.1137c4.8906.335 8.985 4.356 9.0987 9.2706C51.1115 15.644 46.7898 20 41.3304 20h-.1137c-1.8197 0-3.6395-.6702-5.118-1.787l-8.4163-6.255c-.9099-.8935-.9099-2.2338 0-3.0157z'/%3E%3Cpath stroke='%23B8B8B8' d='M43 6v9m-3-9v9'/%3E%3C/g%3E%3Cg%3E%3Cg fill-rule='nonzero' transform='matrix(-1 0 0 1 51 22)'%3E%3Cuse fill='%23F8F3F7' stroke='%23FFF' stroke-opacity='.75' stroke-width='3' xlink:href='%23b'/%3E%3Cuse stroke='%23000' xlink:href='%23b'/%3E%3C/g%3E%3Cpath stroke='%23000' d='M43 27v9m-3-9v9'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.wc-block-price-filter .wc-block-price-filter__range-input::-ms-thumb:hover {
  background-position-y: 0;
  filter: none;
  transform: scale(1.1);
}

.wc-block-price-filter
  .wc-block-price-filter__range-input:focus::-webkit-slider-thumb {
  background-position-y: 0;
  -webkit-filter: none;
  filter: none;
}

.wc-block-price-filter
  .wc-block-price-filter__range-input:focus::-moz-range-thumb {
  background-position-y: 0;
  filter: none;
}

.wc-block-price-filter .wc-block-price-filter__range-input:focus::-ms-thumb {
  background-position-y: 0;
  filter: none;
}

/* -----------------------------------------
  12. External Plugins
----------------------------------------- */

/* Jetpack Sharing */

.sharedaddy {
  margin-top: 30px;
}

/* WP Instagram */

.null-instagram-feed p {
  padding-top: 10px;
}

.instagram-pics {
  list-style: none;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.instagram-pics li {
  width: 33.33333333333%;
}

.instagram-pics li a {
  display: block;
  color: #4a4a4a;
}

.widget-section .instagram-pics {
  margin-left: -15px;
  margin-right: -15px;
}

.widget-section .instagram-pics li {
  width: 25%;
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 30px;
}

/* Elementor */

.elementor-widget .section-heading .section-title {
  margin-bottom: 0;
}

/* Quick View Woo */

.item-product .quickviewwoo-button-before-price,
.item-product .quickviewwoo-button-before-rating {
  margin: 0 0 10px 0;
}

.item-product .quickviewwoo-button-before-title {
  margin: 0 0 10px 0;
}

.item-product .quickviewwoo-button-before-thumbnail {
  margin-left: 15px;
}

.quickviewwoo-button-before-thumbnail + .onsale {
  top: 61px;
}

/* Variation Swatches for WooCommerce */
#codplugin-checkout .woocommerce-Price-amount {
  display: none;
}
#codplugin-checkout .variation-prices .woocommerce-Price-amount {
  display: inline;
  padding: 0 3px;
}
#codplugin-checkout .single-product-table-wrapper,
#codplugin-checkout .variations {
  padding: 0;
  margin: 0;
}
#codplugin-checkout .single-product-table-wrapper,
#codplugin-checkout .variations td {
  border: none;
  box-shadow: none;
}
#codplugin-checkout .variations td {
  max-width: none;
}
#codplugin-checkout .cfvsw-selected-label {
  display: none;
}
#codplugin-checkout input[type="radio"] {
  cursor: pointer;
}

/* -----------------------------------------
  Styling checkout page 
----------------------------------------- */
.woocommerce-order-details .woocommerce-table--order-details  {
    border: 1px solid #259bea;
    border-width: initial;
}

#place_order {
  width: 100%;
  line-height: 40px;
}

.woocommerce-form-login
  label.woocommerce-form__label.woocommerce-form__label-for-checkbox.woocommerce-form-login__rememberme {
  margin-top: 12px;
}
#order_review .quantity {
  display: inline;
}
#order_review .product-name {
  font-weight: bold;
}
#order_review .qty {
  width: 50px;
  height: 30px;
  background: #fafbfc;
}
#order_review_heading,
.woocommerce-billing-fields h3,
.woocommerce-additional-fields {
  display: none;
}
.woocommerce-checkout-review-order-table th,
.woocommerce-checkout-review-order-table td,
.woocommerce-order-details th,
.woocommerce-order-details td {
  padding: 10px 20px !important;
  border: none;
    border-bottom: 1px solid #d7d7d7;

}
.woocommerce-order-details thead th {
  border-bottom: none;
}
.woocommerce-table__product-name .product-quantity {
  background-color: #259bea;
    display: inline-block;
    font-size: 13px;
    color: #fff;
    padding: 1px 3px;
    border-radius: 3px;
}
#order_review .shop_table .product-name,
.order_details .product-name {
  padding-right: 0px !important;
}
.woocommerce-checkout-review-order-table thead,
.woocommerce-checkout-review-order-table tfoot {
  background: #e4ebf3;
}
.woocommerce-checkout-review-order-table thead th,
.woocommerce-checkout-review-order-table tfoot {
  border: none;
}
#customer_details input,
#customer_details .select2-container .select2-selection--single {
  border: 2px solid #e4ebf3;
  height: 50px;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 50px;
}

.woocommerce-table--order-details tfoot td,
.woocommerce-table--order-details tfoot th {
    border-bottom: 1px solid #d7d7d7;

}
.woocommerce-table--order-details tfoot tr:last-child td,
.woocommerce-table--order-details tfoot tr:last-child th {
  border: none;
  }
.woocommerce-table--order-details tfoot {
    background: #f8f8f8;
}
.woocommerce-table--order-details tfoot th {
  text-align: left;
}

#order_review .shop_table .product-name a {
  color: red;
}
#customer_details .col-1 {
  padding: 0;
}
#order_review {
  margin-top: 30px;
}
.woocommerce table.shop_table td ul.wc-item-meta {
  list-style: none;
  margin: 0;
}
.woocommerce table.shop_table td ul.wc-item-meta li {
  display: inline-block;
  padding: 0 20px 0 0;
}
.woocommerce table.shop_table td ul.wc-item-meta p {
  display: inline;
}
.woocommerce-checkout-review-order-table td {
  width: 65%;
}

@media (min-width: 768px) {
  #customer_details .col-1 {
    padding-left: 15px;
    padding-right: 15px;
  }

  #customer_details {
    width: 60%;
    float: left;
  }
  #customer_details .col-2 {
    display: none;
  }
  #customer_details .col-1 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  #order_review_heading {
    width: 30%;
    float: left;
    margin-left: 2%;
  }
  #order_review {
    width: 38%;
    float: left;
    margin-left: 2%;
  }
  .woocommerce .col2-set .col-1,
  .woocommerce-page .col2-set .col-1,
  .woocommerce .col2-set .col-2,
  .woocommerce-page .col2-set .col-2 {
    width: 100%;
  }
}
/* -----------------------------------------
  13. Grid Effects
----------------------------------------- */

.row-effect {
  position: relative;
}

.row-effect::before {
  border: 6px solid rgba(74, 74, 74, 0.35);
  border-top-color: rgba(74, 74, 74, 0.875);
  border-radius: 100%;
  height: 40px;
  width: 40px;
  -webkit-animation: rot 0.8s infinite linear;
  animation: rot 0.8s infinite linear;
  content: "";
  position: absolute;
  left: 50%;
  top: 30px;
  margin-left: -20px;
}

.row-effect.row-images-loaded::before {
  display: none;
}

.row-effect > [class^="col"] {
  opacity: 0;
}

.row-effect > [class^="col"].at-grid-shown {
  opacity: 1;
}

/* Fade In
========================================= */

@-webkit-keyframes at-fade-in {
  0% {
  }

  100% {
    opacity: 1;
  }
}

@keyframes at-fade-in {
  0% {
  }

  100% {
    opacity: 1;
  }
}

.row-effect-fade-in .at-grid-animate {
  -webkit-animation: at-fade-in 0.65s ease forwards;
  animation: at-fade-in 0.65s ease forwards;
}

/* Move Up
========================================= */

@-webkit-keyframes at-move-up {
  0% {
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes at-move-up {
  0% {
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

.row-effect-move-up .at-grid-animate {
  -webkit-transform: translateY(200px);
  transform: translateY(200px);
  -webkit-animation: at-move-up 0.65s ease forwards;
  animation: at-move-up 0.65s ease forwards;
}

/* Scale Up
========================================= */

@-webkit-keyframes at-scale-up {
  0% {
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes at-scale-up {
  0% {
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

.row-effect-scale-up .at-grid-animate {
  -webkit-transform: scale(0.6);
  transform: scale(0.6);
  -webkit-animation: at-scale-up 0.65s ease-in-out forwards;
  animation: at-scale-up 0.65s ease-in-out forwards;
}

/* Fall Perspective
========================================= */

@-webkit-keyframes at-fall-perspective {
  0% {
  }

  100% {
    -webkit-transform: translateZ(0) translateY(0) rotateX(0);
    transform: translateZ(0) translateY(0) rotateX(0);
    opacity: 1;
  }
}

@keyframes at-fall-perspective {
  0% {
  }

  100% {
    -webkit-transform: translateZ(0) translateY(0) rotateX(0);
    transform: translateZ(0) translateY(0) rotateX(0);
    opacity: 1;
  }
}

.row-effect-fall-perspective {
  -webkit-perspective: 1300px;
  perspective: 1300px;
}

.row-effect-fall-perspective .at-grid-animate {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform: translateZ(400px) translateY(300px) rotateX(-90deg);
  transform: translateZ(400px) translateY(300px) rotateX(-90deg);
  -webkit-animation: at-fall-perspective 0.8s ease-in-out forwards;
  animation: at-fall-perspective 0.8s ease-in-out forwards;
}

/* Fly Up
========================================= */

@-webkit-keyframes at-fly-up {
  0% {
  }

  100% {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    opacity: 1;
  }
}

@keyframes at-fly-up {
  0% {
  }

  100% {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    opacity: 1;
  }
}

.row-effect-fly-up {
  -webkit-perspective: 1300px;
  perspective: 1300px;
}

.row-effect-fly-up .at-grid-animate {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform-origin: 50% 50% -300px;
  transform-origin: 50% 50% -300px;
  -webkit-transform: rotateX(-180deg);
  transform: rotateX(-180deg);
  -webkit-animation: at-fly-up 0.8s ease-in-out forwards;
  animation: at-fly-up 0.8s ease-in-out forwards;
}

/* Flip
========================================= */

@-webkit-keyframes at-flip {
  0% {
  }

  100% {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    opacity: 1;
  }
}

@keyframes at-flip {
  0% {
  }

  100% {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
    opacity: 1;
  }
}

.row-effect-flip {
  -webkit-perspective: 1300px;
  perspective: 1300px;
}

.row-effect-flip .at-grid-animate {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: rotateX(-80deg);
  transform: rotateX(-80deg);
  -webkit-animation: at-flip 0.8s ease-in-out forwards;
  animation: at-flip 0.8s ease-in-out forwards;
}

/* Pop Up
========================================= */

@-webkit-keyframes at-pop-up {
  0% {
  }

  70% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
    opacity: 0.8;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes at-pop-up {
  0% {
  }

  70% {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
    opacity: 0.8;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}

.row-effect-pop-up {
  -webkit-perspective: 1300px;
  perspective: 1300px;
}

.row-effect-pop-up .at-grid-animate {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform: scale(0.4);
  transform: scale(0.4);
  -webkit-animation: at-pop-up 0.8s ease-in forwards;
  animation: at-pop-up 0.8s ease-in forwards;
}

/* -----------------------------------------
  14. Utilities
----------------------------------------- */

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.justify-content-center {
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.align-items-start {
  -webkit-box-align: start !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -webkit-box-align: end !important;
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
}

.sr-only,
.screen-reader-text {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus,
.screen-reader-text-focusable:active,
.screen-reader-text-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

.text-justify {
  text-align: justify !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

.hidden-xs-up {
  display: none !important;
}

.hidden-xl-down {
  display: none !important;
}

.text-theme {
  color: #259bea;
}

/* -----------------------------------------
  15. Global Mediaqueries
----------------------------------------- */

@media (min-width: 576px) and (min-width: 1350px) {
  .woocommerce-billing-fields__field-wrapper,
  .woocommerce-shipping-fields__field-wrapper {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-billing-fields p,
  .woocommerce-shipping-fields p,
  .woocommerce-additional-field p {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-billing-fields .form-row-first,
  .woocommerce-billing-fields .form-row-last,
  .woocommerce-shipping-fields .form-row-first,
  .woocommerce-shipping-fields .form-row-last,
  .woocommerce-additional-field .form-row-first,
  .woocommerce-additional-field .form-row-last {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 576px) {
  .head-mast-container,
  .head-search-container {
    padding-right: 15px;
    padding-left: 15px;
  }

  .head-mast-container {
    width: 540px;
    max-width: 100%;
  }

  .gallery {
    margin-right: -5px;
    margin-left: -5px;
  }

  .gallery-columns-1 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-2 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-3 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-4 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-5 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-6 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-7 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-8 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-9 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .type-product .thumbnails {
    margin-right: -15px;
    margin-left: -15px;
  }

  .type-product .thumbnails a {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-billing-fields__field-wrapper,
  .woocommerce-shipping-fields__field-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-billing-fields p,
  .woocommerce-shipping-fields p,
  .woocommerce-additional-field p {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .woocommerce-billing-fields .form-row-first,
  .woocommerce-billing-fields .form-row-last,
  .woocommerce-shipping-fields .form-row-first,
  .woocommerce-shipping-fields .form-row-last,
  .woocommerce-additional-field .form-row-first,
  .woocommerce-additional-field .form-row-last {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .place-order {
    text-align: right;
  }

  .woocommerce-account .woocommerce:not(.widget) {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-account .woocommerce:not(.widget) > h2,
  .woocommerce-account .woocommerce:not(.widget) > form.login {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-navigation {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-content {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Addresses {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }

  .wc-block-grid .wc-block-grid__products {
    margin-right: -15px;
    margin-left: -15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .has-2-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-2-columns .wc-block-grid__products .wc-block-grid__product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .flex-sm-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .justify-content-sm-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .align-items-sm-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .text-sm-left {
    text-align: left !important;
  }

  .text-sm-right {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }

  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }

  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }

  .hidden-sm-up {
    display: none !important;
  }
}

@media (min-width: 576px) and (min-width: 992px) {
  .woocommerce-billing-fields__field-wrapper,
  .woocommerce-shipping-fields__field-wrapper {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-billing-fields p,
  .woocommerce-shipping-fields p,
  .woocommerce-additional-field p {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-billing-fields .form-row-first,
  .woocommerce-billing-fields .form-row-last,
  .woocommerce-shipping-fields .form-row-first,
  .woocommerce-shipping-fields .form-row-last,
  .woocommerce-additional-field .form-row-first,
  .woocommerce-additional-field .form-row-last {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 576px) and (min-width: 768px) {
  .woocommerce-billing-fields__field-wrapper,
  .woocommerce-shipping-fields__field-wrapper {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-billing-fields p,
  .woocommerce-shipping-fields p,
  .woocommerce-additional-field p {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-billing-fields .form-row-first,
  .woocommerce-billing-fields .form-row-last,
  .woocommerce-shipping-fields .form-row-first,
  .woocommerce-shipping-fields .form-row-last,
  .woocommerce-additional-field .form-row-first,
  .woocommerce-additional-field .form-row-last {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 576px) and (min-width: 576px) {
  .woocommerce-billing-fields__field-wrapper,
  .woocommerce-shipping-fields__field-wrapper {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-billing-fields p,
  .woocommerce-shipping-fields p,
  .woocommerce-additional-field p {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-billing-fields .form-row-first,
  .woocommerce-billing-fields .form-row-last,
  .woocommerce-shipping-fields .form-row-first,
  .woocommerce-shipping-fields .form-row-last,
  .woocommerce-additional-field .form-row-first,
  .woocommerce-additional-field .form-row-last {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px) and (min-width: 768px) {
  .head-mast-row {
    margin-right: -15px;
    margin-left: -15px;
  }

  .header-branding-wrap {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce .col2-set {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals {
    margin-right: -15px;
    margin-left: -15px;
  }

  .cart-collaterals .cross-sells {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals .cart_totals {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px) and (min-width: 992px) {
  .head-mast-row {
    margin-right: -15px;
    margin-left: -15px;
  }

  .header-branding-wrap {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce .col2-set {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals {
    margin-right: -15px;
    margin-left: -15px;
  }

  .cart-collaterals .cross-sells {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals .cart_totals {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px) and (min-width: 1350px) {
  .head-mast-row {
    margin-right: -15px;
    margin-left: -15px;
  }

  .header-branding-wrap {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce .col2-set {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals {
    margin-right: -15px;
    margin-left: -15px;
  }

  .cart-collaterals .cross-sells {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals .cart_totals {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 768px) {
  .head-mast-container {
    padding-right: 15px;
    padding-left: 15px;
  }

  .head-mast-container,
  .head-search-container {
    width: 720px;
    max-width: 100%;
  }

  .head-mast-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .header-branding-wrap {
    padding-right: 15px;
    padding-left: 15px;
  }
  .head-search-form-wrap {
    display: none;
    position: relative;
    width: 100%;
    min-height: 1px;
  }

  .entry-content-column-split {
    -webkit-columns: 2;
    -moz-columns: 2;
    columns: 2;
  }

  .item-media .item-title {
    font-size: 28px;
  }

  .item-media-sm .item-title {
    font-size: 14px;
  }

  .comment-content {
    min-height: 50px;
  }

  .gallery {
    margin-right: -5px;
    margin-left: -5px;
  }

  .gallery-columns-1 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-2 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-3 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-4 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-5 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-6 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-7 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-8 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-9 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .woocommerce .col2-set {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .type-product .thumbnails {
    margin-right: -15px;
    margin-left: -15px;
  }

  .type-product .thumbnails a {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .cart-collaterals .cross-sells {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }

  .cart-collaterals .cart_totals {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
    margin-left: auto;
  }

  .woocommerce-checkout .login .form-row-first,
  .woocommerce-checkout .login .form-row-last {
    float: left;
    width: 49%;
    margin-right: 2%;
  }

  .woocommerce-thankyou-order-details li,
  .wc-bacs-bank-details li {
    float: left;
    width: 25%;
    margin: 0;
  }

  .woocommerce-account .woocommerce:not(.widget) {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-account .woocommerce:not(.widget) > h2,
  .woocommerce-account .woocommerce:not(.widget) > form.login {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-navigation {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-navigation {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .woocommerce-MyAccount-content {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-content {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .woocommerce-Addresses {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Address {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .wc-block-grid .wc-block-grid__products {
    margin-right: -15px;
    margin-left: -15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-2-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .flex-md-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .justify-content-md-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .align-items-md-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-md-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-md-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .text-md-left {
    text-align: left !important;
  }

  .text-md-right {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }

  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }

  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }

  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }

  .hidden-md-up {
    display: none !important;
  }
}

@media (min-width: 768px) and (min-width: 576px) {
  .head-mast-row {
    margin-right: -15px;
    margin-left: -15px;
  }

  .header-branding-wrap {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce .col2-set {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals {
    margin-right: -15px;
    margin-left: -15px;
  }

  .cart-collaterals .cross-sells {
    padding-right: 15px;
    padding-left: 15px;
  }

  .cart-collaterals .cart_totals {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 991px) {
  .sidebar-drawer.with-drawer {
    -webkit-transition: none;
    transition: none;
  }
}

@media (min-width: 992px) {
  .head-mast-container {
    padding-right: 15px;
    padding-left: 15px;
  }

  .head-mast-container,
  .head-search-container {
    width: 940px;
    max-width: 100%;
  }

  .page-hero-content {
    width: 50%;
  }

  .page-hero-align-center .page-hero-content {
    margin-left: auto;
    margin-right: auto;
  }

  .page-hero-align-right .page-hero-content {
    margin-left: auto;
  }

  .block-item-xl .block-item-title,
  .block-item-lg .block-item-title {
    font-size: 36px;
  }

  .block-item-xl .block-item-subtitle,
  .block-item-lg .block-item-subtitle {
    font-size: 20px;
  }

  .block-item-xl .btn-block-item,
  .block-item-lg .btn-block-item {
    padding: 13px 28px;
    font-size: 16px;
  }

  .entry-social-share li {
    display: block;
  }

  .sidebar-drawer.with-drawer {
    position: relative;
    top: 0;
    left: 0;
    width: auto;
    box-shadow: none;
  }

  .with-drawer .sidebar-drawer-header {
    display: none;
  }

  .with-drawer .sidebar-drawer-content {
    height: auto;
    padding: 0;
    overflow-y: visible;
  }

  .gallery {
    margin-right: -5px;
    margin-left: -5px;
  }

  .gallery-columns-1 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-2 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-3 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-4 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-5 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-6 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-7 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-8 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-9 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .with-sidebar .shop-action-results {
    text-align: left;
  }

  .with-sidebar .shop-filter-toggle {
    display: none;
  }

  .type-product .thumbnails {
    margin-right: -15px;
    margin-left: -15px;
  }

  .type-product .thumbnails a {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-account .woocommerce:not(.widget) {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-account .woocommerce:not(.widget) > h2,
  .woocommerce-account .woocommerce:not(.widget) > form.login {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-navigation {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-content {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-content {
    padding-left: 45px;
  }

  .woocommerce-Addresses {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }

  .wc-block-grid .wc-block-grid__products {
    margin-right: -15px;
    margin-left: -15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  .has-2-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .flex-lg-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .justify-content-lg-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .align-items-lg-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .text-lg-left {
    text-align: left !important;
  }

  .text-lg-right {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }

  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }

  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }

  .hidden-lg-up {
    display: none !important;
  }
}

@media (min-width: 1350px) {
  .navigation-main > .menu-item-first {
    width: 205px;
  }

  .header-fullwidth .container,
  .header-fullwidth .head-mast-container {
    width: 1600px;
  }

  .head-mast-container,
  .head-search-container {
    padding-right: 15px;
    padding-left: 15px;
  }

  .head-mast-container,
  .head-search-container {
    width: 1130px;
    max-width: 100%;
  }

  .footer-fullwidth .container {
    width: 1600px;
  }

  .sidebar:not(.sidebar-drawer) {
    margin-left: 30px;
  }

  .flex-row-reverse .sidebar:not(.sidebar-drawer) {
    margin-left: 0;
    margin-right: 30px;
  }

  .gallery {
    margin-right: -5px;
    margin-left: -5px;
  }

  .gallery-columns-1 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-2 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-3 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-4 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-5 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-6 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-7 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-8 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .gallery-columns-9 .gallery-item {
    padding-right: 5px;
    padding-left: 5px;
  }

  .type-product .thumbnails {
    margin-right: -15px;
    margin-left: -15px;
  }

  .type-product .thumbnails a {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-account .woocommerce:not(.widget) {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-account .woocommerce:not(.widget) > h2,
  .woocommerce-account .woocommerce:not(.widget) > form.login {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-navigation {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-MyAccount-content {
    padding-right: 15px;
    padding-left: 15px;
  }

  .woocommerce-Addresses {
    margin-right: -15px;
    margin-left: -15px;
  }

  .woocommerce-Address {
    padding-right: 15px;
    padding-left: 15px;
  }

  .wc-block-grid .wc-block-grid__products {
    margin-right: -15px;
    margin-left: -15px;
  }

  .has-4-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-3-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .has-2-columns .wc-block-grid__products .wc-block-grid__product {
    padding-right: 15px;
    padding-left: 15px;
  }

  .flex-xl-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .justify-content-xl-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .align-items-xl-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .text-xl-left {
    text-align: left !important;
  }

  .text-xl-right {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }

  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }

  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }

  .hidden-xl-up {
    display: none !important;
  }
}

@media (max-width: 1349px) {
  .page-hero {
    height: 250px;
  }

  .entry-content blockquote {
    font-size: 22px;
  }

  .item-media .item-thumb {
    width: 220px;
  }

  .item-media-sm .item-thumb {
    width: 70px;
  }

  .hidden-lg-down {
    display: none !important;
  }
}

@media (max-width: 991px) {
  .head-mast-row {
      flex-flow: row;
  }
  .head-mast-row .product-name {
    font-size: 18px;
  }
    .mobile-menu-list {
        list-style: none;
        padding: 0;
        margin-top: 10px;
    }

    .mobile-menu-list  a {
        border-bottom: 1px solid #e1e1e1;
    }

    .mobile-menu-list li a {
        display: block;
        color: #444;
        padding: 10px 15px;
        text-decoration: none;
    }

    .mobile-menu-list .sub-menu {
        display: none;
        list-style: none;
        padding: 0px;
        background: #f4f4f4;
    }
    .mobile-menu-list .sub-menu .sub-menu{
          background: #eaeaea;
    }
    .mobile-menu-list .sub-menu li {
        border-bottom: none;
    }

    .mobile-menu-list .sub-menu a {
        padding: 10px;
    }
  .head-nav {
    display: none !important;
  }

  .site-logo {
    font-size: 26px;
  }

  .site-tagline {
    display: none;
  }

  .head-mast-row {
    height: 60px;
  }
  .custom-logo-link img {
      max-height: 55px;
  }
  .head-mast-row .product-name {
    border: none;
  }
  .head-mast-row .star-rating {
    margin-top: 2px;
  }
  .head-mast-row .product-price ins {
    display: block;
  }
  .head-mast-row .product-price span {
    padding: 5px 10px;
  }
  .mobile-nav-trigger,
  .header-mini-cart-trigger,
  .header-search-icon {
    height: 40px;
    width: 40px;
  }
  .mobile-nav-trigger {
    position: absolute;
    left: 15px;
  }  
  .header-branding-wrap {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    z-index: 0;
  }
  .header-mini-cart-trigger {
    padding-left: 10px;
    line-height: 48px;
  }
  .header-search-icon {
    padding: 11px;
  }
  .category-search-select {
    display: none;
  }

  .header-branding-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .page-hero {
    height: 200px;
  }

  .page-hero-lg {
    height: 300px;
  }

  .page-hero-align-top {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .page-hero-align-bottom {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .page-hero-title {
    font-size: 30px;
  }

  .page-hero-subtitle {
    font-size: 20px;
  }

  .page-hero-slideshow {
    height: 300px;
  }

  .page-hero-slideshow-nav-wrap {
    display: none;
  }

  .main {
    padding: 50px 0;
  }

  .sidebar {
    margin-top: 80px;
  }

  .widget-section {
    margin-bottom: 50px;
  }

  .section-padded {
    padding-top: 50px;
  }

  .entry-thumb {
    margin-bottom: 30px;
  }

  .entry-author-thumbnail {
    width: 100px;
    margin-right: 15px;
  }

  .item-media {
    margin-bottom: 50px;
  }

  .item-media .item-thumb {
    width: 180px;
  }

  .item-media-sm .item-thumb {
    width: 70px;
  }

  .block-item-content {
    padding: 15px;
  }

  .block-item-title {
    font-size: 22px;
  }

  .entry-social-share {
    text-align: left;
    margin-bottom: 30px;
  }

  .footer-widgets {
    padding: 50px 0 0px;
  }

  .sidebar:not(.sidebar-drawer) {
    padding: 0;
    margin-top: 60px;
  }

  .sidebar-drawer-visible {
    margin: 0;
  }

  .widget {
    margin-bottom: 50px;
  }

  .widget_at-home-newsletter {
    padding: 0;
  }

  .widget-newsletter-form {
    margin-top: 20px;
  }

  .gallery-columns-6 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  .gallery-columns-7 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  .gallery-columns-8 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  .gallery-columns-9 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  .mobile-nav-trigger {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }

  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .type-product .entry-summary {
    margin-bottom: 40px;
  }

  .entry-product-info {
    margin-top: 0;
    padding: 20px;
    margin-bottom: 50px;
  }

  .single-product-table-wrapper {
    padding: 15px;
    font-size: 14px;
  }

  .woocommerce-tabs-container {
    padding: 50px 0;
  }

  .woocommerce-tabs-container-narrow {
    padding: 30px;
  }

  .wc-form-login {
    margin: 0 15px;
  }

  .wc-form-login .woocommerce-form-login {
    margin-bottom: 30px;
  }

  .widget-section .instagram-pics {
    margin: 0;
  }

  .widget-section .instagram-pics li {
    padding: 0;
    margin: 0;
  }

  .hidden-md-down {
    display: none !important;
  }
  .callus-icon {
    bottom: 30px;
  }
}

@media (max-width: 767px) {
  .custom-logo-link img {
    max-height: 50px;
  }
  .head-mast {
    padding: 0;
  }

  .head-mast-container {
    width: 100%;
    padding: 0;
  }
  .top-head-wrap .container {
    width: 100%;
  }
  .header-branding-wrap {
    padding: 15px;
  }

  .page-hero {
    height: 150px;
  }

  .page-hero-lg {
    height: 300px;
  }

  .page-hero-title {
    font-size: 26px;
  }

  .page-hero-subtitle {
    font-size: 16px;
    line-height: normal;
  }

  .page-hero-slideshow {
    height: 300px;
  }

  .napoleon-slick-slider .slick-arrow {
    width: 38px;
    height: 38px;
  }

  .main {
    padding: 35px 0;
  }

  .widget-section {
    margin-bottom: 35px;
  }

  .section-padded {
    padding-top: 35px;
  }

  .section-title {
    font-size: 22px;
    line-height: 1;
    margin-bottom: 5px;
  }

  .entry-title {
    font-size: 24px;
    line-height: 1;
    margin-bottom: 10px;
  }

  .entry-meta {
    font-size: 14px;
  }

  .entry-content blockquote {
    font-size: 18px;
    padding-left: 30px;
    margin: 35px 0 35px 10px;
  }

  .entry-content .alignleft,
  .entry-content .alignright {
    display: block;
    float: none;
    margin-left: auto;
    margin-right: auto;
  }

  .entry-author-box {
    display: block;
  }

  .entry-author-thumbnail {
    width: 140px;
    margin: 0 0 10px;
  }

  .item-media {
    margin-bottom: 35px;
  }

  .item-media .item-thumb {
    width: 150px;
    margin-right: 20px;
  }

  .item-media-sm .item-thumb {
    width: 70px;
    margin-right: 15px;
  }

  .row-slider-nav .slick-arrow {
    width: 38px;
    height: 38px;
  }

  .comment-list .children,
  .commentlist .children {
    margin: 0;
    padding: 0;
  }

  .comment-reply-link {
    margin: 0;
  }

  .sidebar-drawer-content {
    padding: 20px;
  }

  .sidebar-drawer-visible {
    width: 100%;
  }

  .widget {
    margin-bottom: 40px;
  }

  .widget_at-home-newsletter {
    padding: 0;
  }

  .gallery-columns-1 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-2 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-3 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-4 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-5 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-6 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-7 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-8 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gallery-columns-9 .gallery-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .woocommerce-message,
  .woocommerce-error,
  .woocommerce-info,
  .woocommerce-noreviews {
    font-size: 14px;
  }

  .woocommerce-message {
    padding-bottom: 45px;
  }

  .woocommerce-message .button {
    float: none;
    position: absolute;
    display: block;
    padding: 0;
    left: 45px;
    bottom: 15px;
    margin: 0;
  }

  .shop-actions {
    display: block;
  }

  .shop-actions .woocommerce-ordering {
    width: 100%;
  }

  .shop-actions .woocommerce-ordering select {
    width: 100%;
  }

  .shop-action-results {
    margin-bottom: 15px;
  }

  .shop-filter-toggle {
    display: block;
    margin: 0 0 15px;
  }

  .type-product .entry-summary {
    margin-bottom: 35px;
  }

  .product_meta {
    display: block;
    margin-bottom: 20px;
  }

  .product_meta > span {
    display: block;
  }

  .entry-product-info {
    margin-bottom: 35px;
  }

  .woocommerce-grouped-product-list tr td:first-child {
    width: auto;
  }

  .group_table .quantity label {
    display: none;
  }

  .woocommerce-tabs-container {
    padding: 35px 0;
  }

  .woocommerce-tabs-container-narrow {
    padding: 15px;
  }

  .wc-tabs {
    display: block;
  }

  .wc-tabs a {
    display: block;
  }

  .woocommerce-Reviews .avatar {
    display: none;
  }

  .shop_table .product-thumbnail {
    display: none;
  }

  .shop_table .product-price {
    display: none;
  }

  .shop_table .product-quantity .quantity label {
    display: none;
  }

  .shop_table .product-quantity .quantity .qty {
    max-width: 50px;
  }

  .cart-collaterals .cross-sells {
    margin-bottom: 50px;
  }

  .cart-collaterals .cart_totals {
    clear: both;
  }

  .place-order .terms {
    margin: 5px 0 0;
    display: block;
  }

  .widget-section .instagram-pics li {
    width: 50%;
  }

  .hidden-sm-down {
    display: none !important;
  }
}

@media (max-width: 575px) {
  input,
  textarea,
  select {
    width: 100%;
  }

  .page-hero .container {
    width: 100%;
  }

  .item-media {
    display: block;
  }

  .item-media.item-media-sm {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .item-media .item-thumb {
    width: 100%;
    margin-bottom: 25px;
  }

  .item-media-sm .item-thumb {
    width: 70px;
    margin: 0 15px 0 0;
  }

  .comment-author .avatar {
    display: none;
  }

  .shop_table .coupon {
    display: block;
    float: none;
    width: 100%;
    margin-bottom: 15px;
  }

  .hidden-xs-down {
    display: none !important;
  }
  .top-menu {
    display: none;
  }
  .callus-icon {
    right: 10px;
  }
}

@media (max-width: 991px) and (min-width: 576px) {
  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (max-width: 991px) and (min-width: 768px) {
  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (max-width: 991px) and (min-width: 992px) {
  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (max-width: 991px) and (min-width: 1350px) {
  .woocommerce .col2-set .col-1,
  .woocommerce .col2-set .col-2 {
    padding-right: 15px;
    padding-left: 15px;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .item-product .added_to_cart {
    margin-top: 10px;
  }
}

@media (max-width: 992px) {
  .widget_at-home-latest-products .row-items.columns-5,
  .widget_at-home-latest-products .row-items.columns-4,
  .woocommerce-Reviews .commentlist,
  .thanks-products {
    grid-template-columns: repeat(3, 1fr);
  }
  .item-content {
    padding: 10px 15px 10px;
  }
}

@media (max-width: 767px) {
  .widget_at-home-latest-products .row-items.columns-5,
  .widget_at-home-latest-products .row-items.columns-4,
  .widget_at-home-latest-products .row-items.columns-3,
  .woocommerce-Reviews .commentlist,
  .row-items.columns-5,
  .row-items.columns-4,
  .row-items.columns-3 ,
  .thanks-products {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 576px) {
  .footer-copyrights,
  .footer-credit {
    text-align: center;
    margin: 10px 0;
  }
}
@media (max-width: 500px) {
  .header-search-icon {
    display: none;
  }
  .widget_at-home-latest-products .row-items[class*="columns"],
  .row-items[class*="columns"],
  .woocommerce-Reviews .commentlist {
    grid-gap: 10px;
    padding: 0;
  }
  .row.row-items {
    margin: 0;
  }

  .item .price,
  .item-title {
    font-size: 14px;
  }
  .item-product {
    margin-bottom: 7px;
  }
  .widget_at-home-latest-products .item {
    margin-bottom: 17px;
  }
  .wc-proceed-to-checkout a {
    width: 100%;
  }
  #mobilemenu {
    max-width: 85%;
  }
  .radio-variation-prices tr {
    grid-column-gap: 0;
  }

  .head-mast-row .product-name {
    font-size: 15px;
  
  }
  .head-mast-row .product-price {
    font-size: 17px;
  }
  .top-head-center {
    line-height: initial;
    padding: 5px;
  }

}

@media (max-width:450px) {
  .mobile-nav-trigger {
    background: transparent;
    width: 25px;
  }
  .head-mini-cart-wrap {
    padding: 0 7px;
  }
  .header-mini-cart {
    background: none;
  }
  .header-search-icon {
    padding: 11px 4px;
    background: none;
    width: 25px;
  }
}


