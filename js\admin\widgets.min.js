jQuery(document).ready(function(e){"use strict";function i(e,i){a(i)}var t=e("body"),a=function(e){napoleon_repeating_sortable_init(e),napoleon_colorpicker_init(e),napoleon_alpha_colorpicker_init(e),napoleon_collapsible_init(e)};a(),e(document).on("widget-added",i),e(document).ajaxSuccess(function(i,t,o){if(-1!=o.data.search("action=save-widget")){var n;if(null!==(n=o.data.match(/widget-id=(at-.*?-\d+)\b/))){var s=e("input[name='widget-id'][value='"+n[1]+"']").parent();a(s)}}}),t.on("click",".at-collapsible legend",function(){var i=e(this).find("i");i.hasClass("dashicons-arrow-down")?(i.removeClass("dashicons-arrow-down").addClass("dashicons-arrow-right"),e(this).siblings(".elements").slideUp()):(i.removeClass("dashicons-arrow-right").addClass("dashicons-arrow-down"),e(this).siblings(".elements").slideDown())}),t.on("change",".at-repeating-fields .posts_dropdown",function(){e(this).parent().data("value",e(this).val())}),t.on("change",':has(input.id_base[value="at-home-post-type-items"]) .napoleon-post-type-select',function(){var i=e(this).parent().parent(),t=e(this),a=t.parent().data("ajaxposts");e.ajax({type:"post",url:ThemeWidget.ajaxurl,data:{action:a,post_type_name:t.val(),name_field:t.attr("name"),nonce:ThemeWidget.widget_post_type_items_nonce},dataType:"text",beforeSend:function(){i.find(".post-field").addClass("loading"),i.find(".at-repeating-fields .posts_dropdown").prop("disabled","disabled").css("opacity","0.5")},success:function(t){var a=i.find(".at-repeating-fields .posts_dropdown");""!=t?(a.html(t),a.each(function(){e(this).val(e(this).parent().data("value"))}),a.removeAttr("disabled").css("opacity","1")):a.html("").prop("disabled","disabled").css("opacity","0.5"),i.find(".post-field").removeClass("loading")}})})});var napoleon_collapsible_init=function(e){void 0===e?(jQuery(".at-collapsible .elements").hide(),jQuery(".at-collapsible legend i").removeClass("dashicons-arrow-down").addClass("dashicons-arrow-right")):(jQuery(".at-collapsible .elements",e).hide(),jQuery(".at-collapsible legend i",e).removeClass("dashicons-arrow-down").addClass("dashicons-arrow-right"))},napoleon_alpha_colorpicker_init=function(e){if(void 0===e){jQuery("#widgets-right .napoleon-alpha-color-picker, #wp_inactive_widgets .napoleon-alpha-color-picker").filter(function(){return!jQuery(this).parents(".field-prototype").length}).alphaColorPicker()}else jQuery(".napoleon-alpha-color-picker",e).alphaColorPicker()},napoleon_colorpicker_init=function(e){if(void 0===e){jQuery("#widgets-right .napoleon-color-picker, #wp_inactive_widgets .napoleon-color-picker").filter(function(){return!jQuery(this).parents(".field-prototype").length}).each(function(){jQuery(this).wpColorPicker({change:_.throttle(function(){jQuery(this).trigger("change")},1e3,{leading:!1})})})}else jQuery(".napoleon-color-picker",e).each(function(){jQuery(this).wpColorPicker({change:_.throttle(function(){jQuery(this).trigger("change")},1e3,{leading:!1})})})};