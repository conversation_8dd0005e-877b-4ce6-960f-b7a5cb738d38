<?php


function codplugin_checkout_form($product_id){

    $product = wc_get_product($product_id);
    $pid = $product->get_id();
    $_product = wc_get_product($pid);
    $product_price = $_product->get_price();
    $product_currency = $product->get_price_html();
    $product_type = get_the_terms($pid, "product_type")[0]->slug;


    global $woocommerce;
    $countries_obj = new WC_Countries();
    $countries = $countries_obj->__get("countries");
    $default_country = $countries_obj->get_base_country();
    //$codplugin_country_shortname = get_option("codplugin_country_shortname");
    $default_county_states = $countries_obj->get_states($default_country);

    $delivery_zones = WC_Shipping_Zones::get_zones();

    // Get all your existing shipping zones IDS
    $zone_ids = array_keys([""] + WC_Shipping_Zones::get_zones());


    // Loop through shipping Zones IDs

    $everyprice = 0;

    foreach ($zone_ids as $zone_id) {
        // Get the shipping Zone object
        $shipping_zone = new WC_Shipping_Zone($zone_id);

        // Get all shipping method values for the shipping zone
        $shipping_methods = $shipping_zone->get_shipping_methods(true, "values");

        // Loop through each shipping methods set for the current shipping zone
        foreach ($shipping_methods as $instance_id) {

            if ( 'free_shipping' === $instance_id->id ) {
                        $everyprice = 0;

            } else {

                 $everyprice = $instance_id->cost;
            }
        }
    }


        $checkout_page_id = wc_get_page_id( 'checkout' );
        $codplugin_thanks_url = $checkout_page_id ? get_permalink( $checkout_page_id ) : '';


?>

<div id="codplugin-checkout" >
    <?php if (get_theme_mod( 'add_info' ) ) : ?>
        <div class="codplugin-checkout-title"> 
            <h3><?php echo esc_html( get_theme_mod( 'add_info' ) ); ?></h3>
        </div>
    <?php endif; ?>

	
    <form id="codplugin_woo_single_form" class="checkout woocommerce-checkout" <?php if (get_theme_mod('create_orders_with_php', 0) == 1) echo 'method="POST"'; ?> >

        <input type="hidden" id="rwc-form-nonce" name="rwc-form-nonce" value="<?php echo wp_create_nonce('rwc_form_nonce'); ?>">
        <input type="hidden" id="codplugin_thankyou_url" value="<?php echo $codplugin_thanks_url; ?>">
        <input type="hidden" name="product_id" value="<?php echo $pid; ?>">
        <input type="hidden" name="everyprice" id="everyprice" value="<?php echo $everyprice; ?>">
        <input type="hidden" name="d_price" id="d_price">
        <input type="hidden" name="codplugin_c_number" id="codplugin_c_number" value="1">
        <input type="hidden" name="codplugin_d_method" id="codplugin_d_method" >

        <input type="text" name="full_name" placeholder="<?php echo esc_html( get_theme_mod( 'form_full_name', __('Full Name', 'napoleon' ) ) ); ?>" required autocomplete="off">

        <input type="tel"  <?php if ( get_theme_mod( 'codform_tel_settings' ) ) {echo get_theme_mod( 'codform_tel_settings' ); } ?>  name="phone_number" placeholder="<?php echo esc_html( get_theme_mod( 'form_phone', __('Phone Number', 'napoleon'  ) ) ) ; ?>" required autocomplete="off">


        <?php if ( ! empty ($default_county_states)):  
            if ( get_theme_mod( 'display_city_field', 0)  == 1 && file_exists(get_theme_file_path() . '/inc/order-form/include/states/' . $default_country . '.php') ) : 

                     do_action('codplugin_state_city'); 

                else: ?>           
                <select id='codplugin_state' name="codplugin_state" required >
                    <option value="" disabled selected hidden>
                        <?php echo esc_html( get_theme_mod( 'form_state', __('State', 'napoleon' ) ) ); ?>

                    </option>
                    <?php foreach ($default_county_states as $state) {
                        echo "<option id='codplugin_state_click'>" .$state."</option>";
                    } ?>
                </select>

            <?php endif; 

        else: ?>

            <input class="has-no-states"  type="text" name="codplugin_state" placeholder="<?php echo esc_html( get_theme_mod( 'form_state', __('State', 'napoleon' ) ) ); ?>" required autocomplete="off">
        <?php endif; ?>


        <?php if ( get_theme_mod( 'hide_address_field', 1)  == 0 ) :  ?>

            <input type="text" name="full_address" placeholder="<?php echo esc_html( get_theme_mod( 'form_address', __('Full Address', 'napoleon'  ) ) ); ?>" required autocomplete="off">

        <?php else:?>

            <input type="hidden" name="full_address" >

        <?php endif; ?>

        <?php if ( get_theme_mod( 'hide_order_notes', 0)  == 1 ) :  ?>

        <textarea id="order-notes" name="order_notes" placeholder="<?php echo esc_html( get_theme_mod( 'form_order_notes', __('Add Note', 'napoleon'  ) ) ); ?>"  rows="3" cols="50"></textarea>
		
       <?php else:?>

            <input type="hidden" name="order_notes" >

        <?php endif; ?>

        <?php if ( get_theme_mod( 'hide_email_field', 1)  == 0 ) :  ?>

            <input type="email" name="codplugin_email" placeholder="<?php echo esc_html( get_theme_mod( 'form_email', __('Email', 'napoleon' ) ) ); ?>" required autocomplete="off">

        <?php endif; ?>






    </form>

        <?php 

        if($product_type=='variable') :

            if  ( defined('CFVSW_GLOBAL') && is_product() ) : 
            
                woocommerce_template_single_add_to_cart ();

                ?>

                 <input type="hidden" name="var_id" id="var_id" form="codplugin_woo_single_form"  >
                 <input type="hidden" name="codplugin_price" id="codplugin_price" form="codplugin_woo_single_form">
            <?php

            else : 

                $variations = $product->get_available_variations();
                $default_attributes = $product->get_default_attributes(); ?>

                <div class="variation-prices <?php echo (get_theme_mod('enable_variation_styling', 0) == 1) ? 'radio-variation-prices' : ''; ?>" ><table>
                <?php 
                foreach($variations as $variation){

                    $attributes = wc_get_formatted_variation($variation['attributes'],true, false);

                    $found_variations = 0;

                    foreach($default_attributes as $key => $default_attribute_value ) {
                                    
                        if ($variation['attributes']['attribute_'.$key] == $default_attribute_value) {
                          $found_variations++;
                        }
                    }
                    
                    if (count($default_attributes) == $found_variations) {
                        $default_variation_id = $variation['variation_id'];

                    } 
                    if (isset($default_variation_id) && ($default_variation_id === $variation['variation_id'])) {
                            $default_var_id = "checked";
                            $best_offer_label = "<td class='best-offer'>Most Popular</td>";
                        } else {
                            $default_var_id = "";
                            $best_offer_label ="";
                    }

                    $regular_price = wc_price($variation['display_regular_price']);
                    $sale_price = wc_price($variation['display_price']);

                    $price_html = $sale_price;
                        if ($variation['display_regular_price'] !== $variation['display_price']) {
                        $price_html .= '<del class="crossed-price">' . $regular_price . '</del>';
                    }


                    echo '<tr class="'.$default_var_id.'"><td><input type="radio" required name="var_price" id="'. $variation['variation_id'] .'" value="'. $variation['display_price'] .'" ' . $default_var_id . ' ></td><td>' . $attributes . '</td><td>' . $price_html . '</td></tr>';
                } ?>
                
                </table></div>


                <input type="hidden" name="var_id" id="var_id" form="codplugin_woo_single_form" value="<?php echo isset($default_variation_id) ? $default_variation_id : ''; ?>" >
                <input type="hidden" name="codplugin_price" id="codplugin_price" form="codplugin_woo_single_form"value="<?php echo isset($default_variation_id) ? wc_get_product($default_variation_id)->get_price() : ''; ?>" >
           
            <?php
            endif; ?>

           

        <?php else : ?>
            
            <input type="hidden" name="codplugin_price" id="codplugin_price" value="<?php echo $product_price ; ?>" form="codplugin_woo_single_form">
        
        <?php endif; ?>

        <div id="codplugin_gif">
            <img  loading="lazy"  src="<?php echo get_template_directory_uri() . "/inc/order-form/include/assets/img/processing.gif"; ?>" />
        </div>


        <div class="form-footer clear <?php echo $_product->is_sold_individually() ? 'sold-individual' : ''; ?>">

            <div class="form-qte">
                <span id="codplugin_add_button">+</span>
                <span id="codplugin_count_button">1</span>          
                <span id="codplugin_remove_button">-</span>

            </div>
            

            <?php if ($_product->is_in_stock()) : 
               
                $ip_address = WC_Geolocation::get_ip_address();
                
                if (   ( get_theme_mod('block_ip_reordering')  == 1 &&  the_ip_address_has_order( $ip_address ) ) || ( get_theme_mod('block_cookies_reordering')  == 1 && the_cookies_has_order() )  ): ?>
                    <input id="nrwooconfirm"type="submit" disabled name="submit" value="<?php  _e('You can order after 24 hours', 'napoleon'); ?>" >

                <?php elseif ( get_theme_mod( 'display_atc_button', 0)  == 1 ) :  ?>

                    <?php 

                    $add_to_cart_url = '?add-to-cart='.$product->get_id();

                    $button_text =  esc_html( get_theme_mod( 'form_atc_button', __('Add to cart', 'napoleon' ) ) ); 

                    $link = sprintf( '<a href="%s" rel="nofollow" data-product_id="%s" data-product_sku="%s" data-quantity="%s" class="custom-atc-btn button  add_to_cart_button ajax_add_to_cart product_type_%s" data-product_type="%s" >%s</a>',
                        esc_url( $add_to_cart_url ),
                        esc_attr( $product->get_id() ),
                        esc_attr( $product->get_sku() ),
                        esc_attr( isset( $quantity ) ? $quantity : 1 ),
                        esc_attr( $product->get_type()  ),
                        esc_attr( $product->get_type()  ),
                        esc_html( $button_text )
                    );
                    
                    ?>
                
                    <div id="nrwooconfirm" class="atc-buy-button" >
                        <?php echo $link; ?>

                        <input type="submit" name="codplugin-submit" value="<?php echo esc_html( get_theme_mod( 'form_button', 'Click here to confirm the order' ) ); ?>" form="codplugin_woo_single_form">
                    </div>

                <?php else : ?> 

                    <input id="nrwooconfirm"type="submit" name="codplugin-submit" value="<?php echo esc_html( get_theme_mod( 'form_button', __('Click here to confirm the order', 'napoleon') ) ); ?>" form="codplugin_woo_single_form">
                 
                 <?php endif; ?>


            <?php else : ?>

                 <input id="nrwooconfirm"type="submit" disabled name="submit" value="<?php  _e('Out of stock', 'napoleon'); ?>" >

            <?php endif; ?>


        </div>

        <?php if (get_theme_mod( 'whatsapp_number' ) ) : ?>
        <div class="whatsapp-order-section">
            <button id="whatsapp-orders">
                <i class="fab fa-whatsapp"></i>
                <span><?php echo esc_html( get_theme_mod( 'whatsapp_text', __( 'Order from Whatsapp', 'napoleon' )) ); ?></span>
            </button>
        </div>
    <?php endif; ?>


    <div id="codplugin_order_history" class="clear">
        
        <div id="codplugin_h_right">
            <span id="codplugin_h_o">
              <i  class="fas fa-shopping-cart " ></i>
              <?php _e('Order Summary', 'napoleon'); ?>
            </span>
            
        </div>
       
        <div id="codplugin_h_left">
            <i class="fas fa-chevron-down"></i>      
        </div>
    
    </div>

  <?php if ($product_type == "variable"): ?>

    <div id="codplugin_show_hide">
        <table>
            <tr>
                <td class="summary-product-title"><?php echo $product->get_title(); ?></td>
                <td>              
                    <span id="codplugin_count_number">1</span>
                    <span id="codplugin_v_price"></span>
                    <span> <?php echo get_woocommerce_currency_symbol(); ?></span></td>
            </tr>
             <tr>
                <td>
                    <span>
                    <?php _e("Deliver Price", "napoleon"); ?>
                    </span>
                    <div id="shipping-methods"></div>
                </td>
                <td id="codplugin_d_has_price">
                    <span id="codplugin_d_price">
                        <span class="summary-select-state">
                        <?php if ( ! empty ($default_county_states)) { echo __('Choose', 'napoleon') . ' '.esc_html( get_theme_mod( 'form_state', __('State', 'napoleon' ) ) ); } ?>

                        </span>
                    </span>
                    <span class="codplugin_currency"> <?php echo get_woocommerce_currency_symbol(); ?>
                    </span>
                </td>
                <td id="codplugin_d_free" style="display:none;"><span><?php _e('FREE', 'napoleon'); ?></span></td>

            </tr>
             <tr class="full-price">
                <td><?php _e("Total Price", "napoleon"); ?></td>
                <td><span id="codplugin_total_price"></span> <?php echo get_woocommerce_currency_symbol(); ?></td>
            </tr>
        </table>
    </div>
    
  <?php else: ?>

    <div id="codplugin_show_hide">
        <table>
            <tr>
                <td class="summary-product-title"><?php echo $product->get_title(); ?></td>
                <td>
                    <span id="codplugin_count_number">1</span>
                    <span id="codplugin_v_price"><?php echo $product_price; ?></span>
                    <span> <?php echo get_woocommerce_currency_symbol(); ?></span>
                </td>
            </tr>
             <tr>
                <td>
                    <span>
                    <?php _e("Deliver Price", "napoleon"); ?>
                    </span>
                    <div id="shipping-methods"></div>

                </td>
               <td id="codplugin_d_has_price">
                    <span id="codplugin_d_price">
                        <span class="summary-select-state">
                        <?php if ( ! empty ($default_county_states)) { echo __('Choose', 'napoleon') . ' '.esc_html( get_theme_mod( 'form_state', __('State', 'napoleon' ) ) ); } ?>

                        </span>
                    </span>
                    <span class="codplugin_currency"> <?php echo get_woocommerce_currency_symbol(); ?>
                    </span>
                </td>
                <td id="codplugin_d_free" style="display:none;"><span><?php _e('FREE', 'napoleon'); ?></span></td>
            </tr>
             <tr class="full-price">
                <td><?php _e("Total Price", "napoleon"); ?></td>
               <td  ><span id="codplugin_total_price"></span> <span> <?php echo get_woocommerce_currency_symbol(); ?></span></td>
            </tr>
        </table>
    </div>
  <?php endif; ?>
</div>

<?php
    $upsells = $product->get_upsell_ids();
    if ( get_theme_mod('show_upsells', 0) == 1 && $upsells && get_theme_mod( 'create_orders_with_php', 0)  == 0 )  :   
        $upsell_id = $upsells[0];
    ?>
    <div id="cod-upsell">
        <div id="cod-upsell-box">
            <div id="cod-upsell-loader">
                <img  loading="lazy"  src="<?php echo get_template_directory_uri() . "/inc/order-form/include/assets/img/processing.gif"; ?>" />
            </div>
            <h2 class="cod-upsell-heading">
                <?php 

                if( get_theme_mod('upsell_title') ){
                    echo esc_html( get_theme_mod( 'upsell_title' ) );
                } else {
                    echo esc_html__( 'Wait! Your order is not completed!', 'napoleon' );
                }

                 ?>
            </h2>
            <div class="cod-upsell-product">
                <div class="cod-upsell-product-title">
                <?php
                    $upsell = wc_get_product($upsell_id);
                    echo $upsell->get_title();
                ?>
                </div>

                <img  src="<?php echo get_the_post_thumbnail_url($upsell_id); ?>" class="img-responsive" alt=""/>

                
                <span class="price">
                    <?php echo $upsell->get_price_html(); ?>
                </span> 

                <div id="upsell-submit">
                    <input type="hidden" id="upsell_product_id" name="upsell_product_id" value="<?php echo $upsell_id; ?>">
                    <button id="cod-add-upsell"><?php _e('Add To Cart', 'napoleon'); ?></button>
                    <button id="cod-upsell-cancel"><?php _e('No, Thanks', 'napoleon'); ?></button>
                </div>


            </div>
        </div>
    </div>

<?php endif; 
}
