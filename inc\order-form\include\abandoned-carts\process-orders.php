<?php
// Hook into WooCommerce order status changed to processing
add_action('woocommerce_new_order', 'delete_abandoned_order', 10, 1);

function delete_abandoned_order($order_id) {
    // Check if the session contains abandoned order IDs
    if (isset($_SESSION['abandoned_carts_order_id']) && is_array($_SESSION['abandoned_carts_order_id'])) {

        // Proceed to delete
        foreach ($_SESSION['abandoned_carts_order_id'] as $abandoned_order_id) {
            // Get the order
            $order = wc_get_order($abandoned_order_id);
            
            // Check if the order is valid and its status is pending
            if ($order && $order->get_status() === 'pending') {
                
                // Delete the pending order
                wp_delete_post($abandoned_order_id, true);
            }
        }

        // Remove the order IDs from the session
        unset($_SESSION['abandoned_carts_order_id']);
    } 
}
